# 🎯 AI Trading Bot - Real-Time Forex Analysis System

A comprehensive real-time forex trading signal analysis system built with Node.js, TypeScript, and advanced technical analysis.

## 🚀 Features

- **Real-time Market Data**: TradingView-powered live forex data simulation
- **Technical Analysis**: RSI, MACD, EMA, Bollinger Bands calculations
- **Pattern Recognition**: Candlestick pattern detection (<PERSON>ji, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, etc.)
- **Advanced Signals**: ICT, SMC, Elliott Wave, and Wyckoff methodology integration
- **Signal Generation**: Intelligent trading signal creation with confidence scoring
- **Telegram Alerts**: Automated signal distribution via Telegram Bot
- **Risk Management**: Position sizing recommendations and risk calculations
- **No API Keys Required**: Works with simulated market data for testing and learning

## 📋 Prerequisites

- Node.js (v18 or higher)
- Telegram Bot token (for signal notifications)
- No trading account or API keys required!

## 🛠️ Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ai-trading-bot
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Configure environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Build the project**
   ```bash
   npm run build
   ```

## ⚙️ Configuration

### Required Environment Variables

```env
# TradingView Configuration
TRADINGVIEW_UPDATE_INTERVAL=1000
TRADINGVIEW_SYMBOL=EURUSD
TRADINGVIEW_EXCHANGE=FX_IDC

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### Optional Configuration

- Trading parameters (risk/reward ratios, stop loss, take profit)
- Technical indicator periods
- Signal filtering criteria
- Position sizing methods (fixed, percentage, Kelly criterion)
- Risk management settings
- Trading hours and market filters

## 🚀 Usage

### Development Mode

```bash
npm run dev
```

### Production Mode

```bash
npm run build
npm start
```

### Watch Mode (Auto-restart)

```bash
npm run dev:watch
```

## 📊 API Endpoints

- `GET /health` - Health check
- `GET /api/status` - Service status
- `GET /api/signals` - Recent signals (coming soon)
- `GET /api/analytics` - Performance analytics (coming soon)

## 🏗️ Project Structure

```
src/
├── config/           # Configuration management
├── services/         # Core business logic
│   ├── exness/       # Exness broker API integration
│   ├── indicators/   # Technical indicators
│   ├── patterns/     # Candlestick patterns
│   ├── signals/      # Signal generation and trade execution
│   └── telegram/     # Telegram bot
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
└── index.ts          # Application entry point
```

## 🔧 Technical Stack

- **Backend**: Node.js + Express + TypeScript
- **Market Data**: TradingView-powered simulation engine
- **Technical Analysis**: technicalindicators npm package
- **Signal Generation**: Advanced multi-factor analysis
- **Messaging**: Telegram Bot API
- **Real-time Updates**: Event-driven architecture

## 📈 Trading Features

### Technical Indicators

- RSI (Relative Strength Index)
- EMA (Exponential Moving Average)
- MACD (Moving Average Convergence Divergence)
- Bollinger Bands

### Candlestick Patterns

- Bullish/Bearish Engulfing
- Pin Bar (Hammer/Shooting Star)
- Doji variations
- Pattern strength scoring

### Advanced Analysis

- Price Action analysis
- ICT (Inner Circle Trader) concepts
- SMC (Smart Money Concepts)
- Elliott Wave theory
- Wyckoff methodology
- Multi-timeframe confluence

### Signal Generation

- Intelligent buy/sell signal creation
- Confidence scoring and filtering
- Entry, stop loss, and take profit levels
- Risk/reward ratio calculations
- Position size recommendations
- Real-time signal notifications

## 🔔 Signal Format

```
🔔 FOREX SIGNAL
Pair: EUR/USD
Time: 2024-01-15 14:30:25 UTC
Signal: BUY 📈
Entry: 1.10500
Stop Loss: 1.10300 (-20 pips)
Take Profit: 1.10800 (+30 pips)
Risk/Reward: 1:1.5
Confidence: 85%
Position Size: 0.1 lots (recommended)
Status: READY FOR MANUAL EXECUTION 📋
Indicators: RSI(45), MACD Bullish Cross, EMA Break
```

## 🧪 Testing

```bash
npm test
```

## 📝 Logging

Logs are written to both console and file:

- Console: Colored output for development
- File: `./logs/trading-bot.log`

Log levels: `debug`, `info`, `warn`, `error`

## 🔒 Security

- Helmet.js for security headers
- CORS configuration
- Environment variable validation
- Input sanitization
- Rate limiting (planned)

## 🚀 Deployment

### Docker (Planned)

```bash
docker build -t ai-trading-bot .
docker run -d --env-file .env ai-trading-bot
```

### PM2 (Process Manager)

```bash
npm install -g pm2
pm2 start dist/index.js --name "trading-bot"
```

## 📊 Monitoring

- Health check endpoint
- Comprehensive logging
- Error tracking
- Performance metrics (planned)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.

## ⚠️ Disclaimer

This software is for educational and research purposes only. **Trading forex involves substantial risk and may not be suitable for all investors.** Trading forex can result in significant financial losses. Past performance is not indicative of future results.

**Important Notes:**

- This bot generates signals only - it does not execute trades automatically
- All signals are based on simulated market data for educational purposes
- Always verify signals with real market data before trading
- Never risk more than you can afford to lose
- Signals should be used as educational tools, not trading advice

Always consult with a qualified financial advisor before making trading decisions. Use proper risk management and start with small position sizes when trading manually.

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Review the logs for error details

---

**Happy Trading! 📈💰**
