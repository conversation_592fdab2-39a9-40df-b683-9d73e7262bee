import dotenv from 'dotenv';
import { IndicatorConfig, TradingConfig } from '../types';

// Load environment variables
dotenv.config();

export interface AppConfig {
  // Server Configuration
  port: number;
  nodeEnv: string;

  // TradingView Configuration
  tradingView: {
    updateInterval: number;
    symbol: string;
    exchange: string;
  };

  // Telegram Configuration
  telegram: {
    botToken: string;
    chatId: string;
  };

  // MongoDB Configuration
  mongodb: {
    uri: string;
    dbName: string;
  };

  // Trading Configuration
  trading: TradingConfig;

  // Indicators Configuration
  indicators: IndicatorConfig;

  // Logging Configuration
  logging: {
    level: string;
    filePath: string;
  };

  // WebSocket Configuration
  websocket: {
    reconnectInterval: number;
    maxReconnectAttempts: number;
    pingInterval: number;
  };
}

const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value || defaultValue!;
};

const getEnvNumber = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? parseInt(value, 10) : defaultValue!;
};

const getEnvFloat = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? parseFloat(value) : defaultValue!;
};

export const config: AppConfig = {
  port: getEnvNumber('PORT', 3000),
  nodeEnv: getEnvVar('NODE_ENV', 'development'),

  tradingView: {
    updateInterval: getEnvNumber('TRADINGVIEW_UPDATE_INTERVAL', 1000),
    symbol: getEnvVar('TRADINGVIEW_SYMBOL', 'EURUSD'),
    exchange: getEnvVar('TRADINGVIEW_EXCHANGE', 'FX_IDC')
  },

  telegram: {
    botToken: getEnvVar('TELEGRAM_BOT_TOKEN'),
    chatId: getEnvVar('TELEGRAM_CHAT_ID')
  },

  mongodb: {
    uri: getEnvVar(
      'MONGODB_URI',
      'mongodb://localhost:27017/forex-trading-bot'
    ),
    dbName: getEnvVar('MONGODB_DB_NAME', 'forex-trading-bot')
  },

  trading: {
    symbol: getEnvVar('DEFAULT_SYMBOL', 'EUR/USD'),
    interval: getEnvVar('DEFAULT_INTERVAL', '10s'),
    maxSignalsPerDay: getEnvNumber('MAX_SIGNALS_PER_DAY', 50),
    minConfidenceLevel: getEnvNumber('MIN_CONFIDENCE_LEVEL', 75),
    riskRewardRatio: getEnvFloat('DEFAULT_RISK_REWARD_RATIO', 1.5),
    maxRiskPerTrade: getEnvFloat('MAX_RISK_PER_TRADE', 2),
    stopLossPips: getEnvNumber('STOP_LOSS_PIPS', 20),
    takeProfitPips: getEnvNumber('TAKE_PROFIT_PIPS', 30)
  },

  indicators: {
    rsiPeriod: getEnvNumber('RSI_PERIOD', 14),
    emaPeriod: getEnvNumber('EMA_PERIOD', 20),
    macdFast: getEnvNumber('MACD_FAST', 12),
    macdSlow: getEnvNumber('MACD_SLOW', 26),
    macdSignal: getEnvNumber('MACD_SIGNAL', 9),
    bollingerPeriod: getEnvNumber('BOLLINGER_PERIOD', 20),
    bollingerStdDev: getEnvNumber('BOLLINGER_STDDEV', 2)
  },

  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    filePath: getEnvVar('LOG_FILE_PATH', './logs/trading-bot.log')
  },

  websocket: {
    reconnectInterval: getEnvNumber('WS_RECONNECT_INTERVAL', 5000),
    maxReconnectAttempts: getEnvNumber('WS_MAX_RECONNECT_ATTEMPTS', 10),
    pingInterval: getEnvNumber('WS_PING_INTERVAL', 30000)
  }
};

// Validate critical configuration
export const validateConfig = (): void => {
  const requiredFields = ['telegram.botToken', 'telegram.chatId'];

  for (const field of requiredFields) {
    const keys = field.split('.');
    let value: any = config;

    for (const key of keys) {
      value = value[key];
      if (value === undefined || value === '') {
        throw new Error(
          `Configuration field ${field} is required but not provided`
        );
      }
    }
  }

  console.log('✅ Configuration validation passed');
};

export default config;
