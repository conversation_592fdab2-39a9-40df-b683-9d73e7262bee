// Core Trading Types
export interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface TechnicalIndicators {
  rsi: number;
  ema: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
}

export interface CandlestickPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  strength: number; // 0-100
  detected: boolean;
  timestamp: number;
}

export interface TradingSignal {
  id: string;
  symbol: string;
  timestamp: number;
  type: 'BUY' | 'SELL';
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  confidence: number; // 0-100
  indicators: TechnicalIndicators;
  patterns: CandlestickPattern[];
  reasoning: string;
  status: 'pending' | 'sent' | 'filled' | 'cancelled';
}

// Exness API Types
export interface ExnessWebSocketMessage {
  type: 'tick' | 'heartbeat' | 'error' | 'subscription';
  symbol?: string;
  timestamp?: number;
  bid?: number;
  ask?: number;
  spread?: number;
  volume?: number;
  change?: number;
  changePercent?: number;
}

export interface ExnessSubscription {
  action: 'subscribe' | 'unsubscribe';
  symbols: string[];
  types: string[];
}

export interface ExnessAccount {
  id: string;
  name: string;
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  currency: string;
  leverage: number;
  serverType: 'demo' | 'real';
}

export interface ExnessPosition {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  volume: number;
  openPrice: number;
  currentPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  profit: number;
  swap: number;
  commission: number;
  openTime: number;
  comment?: string;
}

export interface ExnessOrder {
  id: string;
  symbol: string;
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop';
  volume: number;
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  expiration?: number;
  comment?: string;
  magic?: number;
}

export interface ExnessTradeRequest {
  action: 'deal' | 'pending' | 'sltp' | 'modify' | 'remove';
  symbol: string;
  volume: number;
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop';
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  deviation?: number;
  comment?: string;
  magic?: number;
  expiration?: number;
}

export interface ExnessTradeResult {
  retcode: number;
  deal?: string;
  order?: string;
  volume?: number;
  price?: number;
  bid?: number;
  ask?: number;
  comment?: string;
  request_id?: string;
}

// Configuration Types
export interface TradingConfig {
  symbol: string;
  interval: string;
  maxSignalsPerDay: number;
  minConfidenceLevel: number;
  riskRewardRatio: number;
  maxRiskPerTrade: number;
  stopLossPips: number;
  takeProfitPips: number;
}

export interface IndicatorConfig {
  rsiPeriod: number;
  emaPeriod: number;
  macdFast: number;
  macdSlow: number;
  macdSignal: number;
  bollingerPeriod: number;
  bollingerStdDev: number;
}

// Database Models
export interface SignalDocument extends TradingSignal {
  _id?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface LogDocument {
  _id?: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  service: string;
  data?: any;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Telegram Types
export interface TelegramMessage {
  chatId: string;
  text: string;
  parseMode?: 'HTML' | 'Markdown';
}

// WebSocket Connection Types
export interface WebSocketConnection {
  isConnected: boolean;
  reconnectAttempts: number;
  lastPingTime: number;
  subscriptions: string[];
}

// Market Data Types
export interface MarketData {
  symbol: string;
  candles: CandlestickData[];
  indicators: TechnicalIndicators;
  patterns: CandlestickPattern[];
  lastUpdate: number;
}

// Signal Analysis Types
export interface SignalAnalysis {
  priceAction: {
    trend: 'bullish' | 'bearish' | 'sideways';
    strength: number;
    support: number;
    resistance: number;
  };
  ict: {
    orderBlock: boolean;
    fairValueGap: boolean;
    liquidityPool: boolean;
  };
  smc: {
    changeOfCharacter: boolean;
    breakOfStructure: boolean;
    orderFlow: 'bullish' | 'bearish' | 'neutral';
  };
  elliottWave: {
    wave: string;
    degree: string;
    direction: 'impulse' | 'corrective';
  };
  wyckoff: {
    phase: 'accumulation' | 'markup' | 'distribution' | 'markdown';
    position: string;
  };
}

export interface AdvancedSignal extends TradingSignal {
  analysis: SignalAnalysis;
  multiTimeframe: {
    [timeframe: string]: TechnicalIndicators;
  };
}
