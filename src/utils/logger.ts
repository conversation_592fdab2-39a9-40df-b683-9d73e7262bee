import fs from 'fs';
import path from 'path';
import { config } from '../config';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  service: string;
  message: string;
  data?: any;
}

class Logger {
  private logLevel: LogLevel;
  private logFilePath: string;

  constructor() {
    this.logLevel = config.logging.level as LogLevel;
    this.logFilePath = config.logging.filePath;
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    const logDir = path.dirname(this.logFilePath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    };
    return levels[level] >= levels[this.logLevel];
  }

  private formatMessage(entry: LogEntry): string {
    const dataStr = entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : '';
    return `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${entry.service}] ${entry.message}${dataStr}`;
  }

  private writeToFile(message: string): void {
    try {
      fs.appendFileSync(this.logFilePath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private log(level: LogLevel, service: string, message: string, data?: any): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service,
      message,
      data,
    };

    const formattedMessage = this.formatMessage(entry);

    // Console output with colors
    const colors = {
      debug: '\x1b[36m', // Cyan
      info: '\x1b[32m',  // Green
      warn: '\x1b[33m',  // Yellow
      error: '\x1b[31m', // Red
    };
    const reset = '\x1b[0m';
    
    console.log(`${colors[level]}${formattedMessage}${reset}`);

    // File output
    this.writeToFile(formattedMessage);
  }

  debug(service: string, message: string, data?: any): void {
    this.log('debug', service, message, data);
  }

  info(service: string, message: string, data?: any): void {
    this.log('info', service, message, data);
  }

  warn(service: string, message: string, data?: any): void {
    this.log('warn', service, message, data);
  }

  error(service: string, message: string, data?: any): void {
    this.log('error', service, message, data);
  }

  // Convenience methods for specific services
  trading(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'TRADING', message, data);
  }

  websocket(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'WEBSOCKET', message, data);
  }

  telegram(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'TELEGRAM', message, data);
  }

  indicators(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'INDICATORS', message, data);
  }

  patterns(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'PATTERNS', message, data);
  }

  database(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'DATABASE', message, data);
  }
}

export const logger = new Logger();
export default logger;
