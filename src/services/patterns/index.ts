import { EventEmitter } from 'events';
import { logger } from '../../utils/logger';
import { CandlestickData, CandlestickPattern } from '../../types';

export interface PatternDetectionResult {
  symbol: string;
  timestamp: number;
  patterns: CandlestickPattern[];
  candle: CandlestickData;
  previousCandles: CandlestickData[];
}

export interface PatternConfig {
  minBodySize: number; // Minimum body size as percentage of total range
  dojiThreshold: number; // Maximum body size for doji as percentage of total range
  shadowRatio: number; // Minimum shadow to body ratio for pin bars
  engulfingMinSize: number; // Minimum size difference for engulfing patterns
}

export class CandlestickPatternEngine extends EventEmitter {
  private patternConfig: PatternConfig;
  private patternHistory: Map<string, CandlestickPattern[]> = new Map();
  private readonly maxHistoryLength = 100;

  constructor() {
    super();
    this.patternConfig = this.initializeConfig();
  }

  private initializeConfig(): PatternConfig {
    return {
      minBodySize: 0.3, // 30% of total range
      dojiThreshold: 0.1, // 10% of total range
      shadowRatio: 2.0, // Shadow should be at least 2x body size
      engulfingMinSize: 1.2, // Engulfing candle should be 20% larger
    };
  }

  /**
   * Detect patterns in candlestick data
   */
  public detectPatterns(
    symbol: string,
    candles: CandlestickData[],
    lookbackPeriod: number = 3
  ): PatternDetectionResult {
    if (candles.length < 2) {
      return {
        symbol,
        timestamp: Date.now(),
        patterns: [],
        candle: candles[candles.length - 1] || { timestamp: 0, open: 0, high: 0, low: 0, close: 0 },
        previousCandles: [],
      };
    }

    const currentCandle = candles[candles.length - 1];
    const previousCandles = candles.slice(-lookbackPeriod - 1, -1);
    const patterns: CandlestickPattern[] = [];

    try {
      // Single candle patterns
      patterns.push(...this.detectSingleCandlePatterns(currentCandle));

      // Two candle patterns
      if (previousCandles.length >= 1) {
        patterns.push(...this.detectTwoCandlePatterns(previousCandles[previousCandles.length - 1], currentCandle));
      }

      // Three candle patterns
      if (previousCandles.length >= 2) {
        patterns.push(...this.detectThreeCandlePatterns(
          previousCandles[previousCandles.length - 2],
          previousCandles[previousCandles.length - 1],
          currentCandle
        ));
      }

      // Store patterns in history
      this.storePatterns(symbol, patterns);

      const result: PatternDetectionResult = {
        symbol,
        timestamp: currentCandle.timestamp,
        patterns,
        candle: currentCandle,
        previousCandles,
      };

      // Emit pattern detection event
      if (patterns.length > 0) {
        this.emit('patternsDetected', result);
        logger.patterns('info', `Detected ${patterns.length} patterns for ${symbol}`, {
          patterns: patterns.map(p => p.name),
        });
      }

      return result;
    } catch (error) {
      logger.patterns('error', `Failed to detect patterns for ${symbol}`, { error });
      return {
        symbol,
        timestamp: currentCandle.timestamp,
        patterns: [],
        candle: currentCandle,
        previousCandles,
      };
    }
  }

  /**
   * Detect single candle patterns
   */
  private detectSingleCandlePatterns(candle: CandlestickData): CandlestickPattern[] {
    const patterns: CandlestickPattern[] = [];

    // Calculate candle properties
    const body = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const bodyRatio = totalRange > 0 ? body / totalRange : 0;

    // Doji patterns
    if (bodyRatio <= this.patternConfig.dojiThreshold) {
      patterns.push(this.createPattern('Doji', 'neutral', 70, candle.timestamp));
      
      // Specific doji types
      if (upperShadow > lowerShadow * 2) {
        patterns.push(this.createPattern('Gravestone Doji', 'bearish', 75, candle.timestamp));
      } else if (lowerShadow > upperShadow * 2) {
        patterns.push(this.createPattern('Dragonfly Doji', 'bullish', 75, candle.timestamp));
      }
    }

    // Hammer and Hanging Man
    if (body > 0 && lowerShadow > body * this.patternConfig.shadowRatio && upperShadow < body * 0.5) {
      if (candle.close > candle.open) {
        patterns.push(this.createPattern('Hammer', 'bullish', 80, candle.timestamp));
      } else {
        patterns.push(this.createPattern('Hanging Man', 'bearish', 80, candle.timestamp));
      }
    }

    // Inverted Hammer and Shooting Star
    if (body > 0 && upperShadow > body * this.patternConfig.shadowRatio && lowerShadow < body * 0.5) {
      if (candle.close > candle.open) {
        patterns.push(this.createPattern('Inverted Hammer', 'bullish', 75, candle.timestamp));
      } else {
        patterns.push(this.createPattern('Shooting Star', 'bearish', 85, candle.timestamp));
      }
    }

    // Marubozu (long body with little to no shadows)
    if (bodyRatio >= 0.9) {
      if (candle.close > candle.open) {
        patterns.push(this.createPattern('Bullish Marubozu', 'bullish', 85, candle.timestamp));
      } else {
        patterns.push(this.createPattern('Bearish Marubozu', 'bearish', 85, candle.timestamp));
      }
    }

    // Spinning Top
    if (bodyRatio >= 0.1 && bodyRatio <= 0.3 && upperShadow > body && lowerShadow > body) {
      patterns.push(this.createPattern('Spinning Top', 'neutral', 60, candle.timestamp));
    }

    return patterns;
  }

  /**
   * Detect two candle patterns
   */
  private detectTwoCandlePatterns(prev: CandlestickData, current: CandlestickData): CandlestickPattern[] {
    const patterns: CandlestickPattern[] = [];

    const prevBody = Math.abs(prev.close - prev.open);
    const currentBody = Math.abs(current.close - current.open);
    const prevRange = prev.high - prev.low;
    const currentRange = current.high - current.low;

    // Bullish Engulfing
    if (
      prev.close < prev.open && // Previous candle is bearish
      current.close > current.open && // Current candle is bullish
      current.open < prev.close && // Current opens below previous close
      current.close > prev.open && // Current closes above previous open
      currentBody > prevBody * this.patternConfig.engulfingMinSize
    ) {
      patterns.push(this.createPattern('Bullish Engulfing', 'bullish', 90, current.timestamp));
    }

    // Bearish Engulfing
    if (
      prev.close > prev.open && // Previous candle is bullish
      current.close < current.open && // Current candle is bearish
      current.open > prev.close && // Current opens above previous close
      current.close < prev.open && // Current closes below previous open
      currentBody > prevBody * this.patternConfig.engulfingMinSize
    ) {
      patterns.push(this.createPattern('Bearish Engulfing', 'bearish', 90, current.timestamp));
    }

    // Piercing Pattern
    if (
      prev.close < prev.open && // Previous candle is bearish
      current.close > current.open && // Current candle is bullish
      current.open < prev.low && // Current opens below previous low
      current.close > (prev.open + prev.close) / 2 && // Current closes above midpoint of previous
      current.close < prev.open // Current closes below previous open
    ) {
      patterns.push(this.createPattern('Piercing Pattern', 'bullish', 85, current.timestamp));
    }

    // Dark Cloud Cover
    if (
      prev.close > prev.open && // Previous candle is bullish
      current.close < current.open && // Current candle is bearish
      current.open > prev.high && // Current opens above previous high
      current.close < (prev.open + prev.close) / 2 && // Current closes below midpoint of previous
      current.close > prev.open // Current closes above previous open
    ) {
      patterns.push(this.createPattern('Dark Cloud Cover', 'bearish', 85, current.timestamp));
    }

    // Harami patterns
    if (
      prevBody > currentBody &&
      current.high < prev.high &&
      current.low > prev.low
    ) {
      if (prev.close < prev.open && current.close > current.open) {
        patterns.push(this.createPattern('Bullish Harami', 'bullish', 75, current.timestamp));
      } else if (prev.close > prev.open && current.close < current.open) {
        patterns.push(this.createPattern('Bearish Harami', 'bearish', 75, current.timestamp));
      }
    }

    // Tweezer patterns
    if (Math.abs(prev.high - current.high) / Math.max(prevRange, currentRange) < 0.02) {
      patterns.push(this.createPattern('Tweezer Top', 'bearish', 70, current.timestamp));
    }
    if (Math.abs(prev.low - current.low) / Math.max(prevRange, currentRange) < 0.02) {
      patterns.push(this.createPattern('Tweezer Bottom', 'bullish', 70, current.timestamp));
    }

    return patterns;
  }

  /**
   * Detect three candle patterns
   */
  private detectThreeCandlePatterns(
    first: CandlestickData,
    second: CandlestickData,
    third: CandlestickData
  ): CandlestickPattern[] {
    const patterns: CandlestickPattern[] = [];

    // Morning Star
    if (
      first.close < first.open && // First candle is bearish
      Math.abs(second.close - second.open) < (second.high - second.low) * 0.3 && // Second is small body
      third.close > third.open && // Third candle is bullish
      third.close > (first.open + first.close) / 2 // Third closes above midpoint of first
    ) {
      patterns.push(this.createPattern('Morning Star', 'bullish', 95, third.timestamp));
    }

    // Evening Star
    if (
      first.close > first.open && // First candle is bullish
      Math.abs(second.close - second.open) < (second.high - second.low) * 0.3 && // Second is small body
      third.close < third.open && // Third candle is bearish
      third.close < (first.open + first.close) / 2 // Third closes below midpoint of first
    ) {
      patterns.push(this.createPattern('Evening Star', 'bearish', 95, third.timestamp));
    }

    // Three White Soldiers
    if (
      first.close > first.open &&
      second.close > second.open &&
      third.close > third.open &&
      second.close > first.close &&
      third.close > second.close &&
      second.open > first.open &&
      third.open > second.open
    ) {
      patterns.push(this.createPattern('Three White Soldiers', 'bullish', 90, third.timestamp));
    }

    // Three Black Crows
    if (
      first.close < first.open &&
      second.close < second.open &&
      third.close < third.open &&
      second.close < first.close &&
      third.close < second.close &&
      second.open < first.open &&
      third.open < second.open
    ) {
      patterns.push(this.createPattern('Three Black Crows', 'bearish', 90, third.timestamp));
    }

    return patterns;
  }

  /**
   * Create a pattern object
   */
  private createPattern(
    name: string,
    type: 'bullish' | 'bearish' | 'neutral',
    strength: number,
    timestamp: number
  ): CandlestickPattern {
    return {
      name,
      type,
      strength,
      detected: true,
      timestamp,
    };
  }

  /**
   * Store patterns in history
   */
  private storePatterns(symbol: string, patterns: CandlestickPattern[]): void {
    if (!this.patternHistory.has(symbol)) {
      this.patternHistory.set(symbol, []);
    }

    const history = this.patternHistory.get(symbol)!;
    history.push(...patterns);

    // Maintain max history length
    if (history.length > this.maxHistoryLength) {
      history.splice(0, history.length - this.maxHistoryLength);
    }
  }

  /**
   * Get pattern history for a symbol
   */
  public getPatternHistory(symbol: string, limit?: number): CandlestickPattern[] {
    const history = this.patternHistory.get(symbol) || [];
    return limit ? history.slice(-limit) : [...history];
  }

  /**
   * Get recent patterns for a symbol
   */
  public getRecentPatterns(symbol: string, timeframe: number = 3600000): CandlestickPattern[] {
    const history = this.patternHistory.get(symbol) || [];
    const cutoffTime = Date.now() - timeframe;
    return history.filter(pattern => pattern.timestamp > cutoffTime);
  }

  /**
   * Update pattern configuration
   */
  public updateConfig(newConfig: Partial<PatternConfig>): void {
    this.patternConfig = { ...this.patternConfig, ...newConfig };
    logger.patterns('info', 'Pattern configuration updated', { config: this.patternConfig });
  }

  /**
   * Get current configuration
   */
  public getConfig(): PatternConfig {
    return { ...this.patternConfig };
  }

  /**
   * Clear pattern history for a symbol
   */
  public clearHistory(symbol: string): void {
    this.patternHistory.delete(symbol);
    logger.patterns('info', `Cleared pattern history for ${symbol}`);
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.patternHistory.keys());
  }
}

// Export singleton instance
export const candlestickPatternEngine = new CandlestickPatternEngine();
