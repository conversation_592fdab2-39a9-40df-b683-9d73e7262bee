import { EventEmitter } from 'events';
import { logger } from '../../utils/logger';
import { CandlestickData, CandlestickPattern } from '../../types';
import { PatternDetectionResult } from './index';

export interface PatternAnalysis {
  symbol: string;
  timestamp: number;
  patterns: CandlestickPattern[];
  confluence: {
    bullish_patterns: number;
    bearish_patterns: number;
    neutral_patterns: number;
    overall_sentiment: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
  };
  context: {
    trend_alignment: 'with_trend' | 'against_trend' | 'neutral';
    support_resistance: 'at_support' | 'at_resistance' | 'neutral';
    volume_confirmation: boolean;
    pattern_reliability: 'high' | 'medium' | 'low';
  };
  signals: {
    entry_signal: boolean;
    reversal_probability: number;
    continuation_probability: number;
    risk_level: 'low' | 'medium' | 'high';
  };
}

export interface MarketContext {
  trend: 'bullish' | 'bearish' | 'sideways';
  support_level?: number;
  resistance_level?: number;
  volume_average?: number;
  volatility: 'low' | 'medium' | 'high';
}

export class PatternAnalyzer extends EventEmitter {
  private analysisHistory: Map<string, PatternAnalysis[]> = new Map();
  private readonly maxHistoryLength = 50;

  constructor() {
    super();
  }

  /**
   * Analyze detected patterns with market context
   */
  public analyzePatterns(
    patternResult: PatternDetectionResult,
    marketContext: MarketContext,
    priceHistory: CandlestickData[]
  ): PatternAnalysis {
    try {
      const analysis: PatternAnalysis = {
        symbol: patternResult.symbol,
        timestamp: patternResult.timestamp,
        patterns: patternResult.patterns,
        confluence: this.calculateConfluence(patternResult.patterns),
        context: this.analyzeContext(patternResult, marketContext, priceHistory),
        signals: { entry_signal: false, reversal_probability: 0, continuation_probability: 0, risk_level: 'medium' },
      };

      // Analyze signals
      analysis.signals = this.analyzeSignals(analysis, marketContext);

      // Store analysis
      this.storeAnalysis(patternResult.symbol, analysis);

      // Emit analysis event
      this.emit('patternAnalysis', analysis);

      logger.patterns('debug', `Pattern analysis complete for ${patternResult.symbol}`, {
        patterns: patternResult.patterns.length,
        sentiment: analysis.confluence.overall_sentiment,
        confidence: analysis.confluence.confidence,
      });

      return analysis;
    } catch (error) {
      logger.patterns('error', `Failed to analyze patterns for ${patternResult.symbol}`, { error });
      throw error;
    }
  }

  /**
   * Calculate pattern confluence
   */
  private calculateConfluence(patterns: CandlestickPattern[]): PatternAnalysis['confluence'] {
    let bullish_patterns = 0;
    let bearish_patterns = 0;
    let neutral_patterns = 0;
    let totalStrength = 0;

    patterns.forEach(pattern => {
      switch (pattern.type) {
        case 'bullish':
          bullish_patterns++;
          break;
        case 'bearish':
          bearish_patterns++;
          break;
        case 'neutral':
          neutral_patterns++;
          break;
      }
      totalStrength += pattern.strength;
    });

    // Determine overall sentiment
    let overall_sentiment: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (bullish_patterns > bearish_patterns) {
      overall_sentiment = 'bullish';
    } else if (bearish_patterns > bullish_patterns) {
      overall_sentiment = 'bearish';
    }

    // Calculate confidence based on pattern strength and confluence
    const averageStrength = patterns.length > 0 ? totalStrength / patterns.length : 0;
    const dominantPatterns = Math.max(bullish_patterns, bearish_patterns);
    const totalPatterns = patterns.length;
    
    let confidence = 0;
    if (totalPatterns > 0) {
      const strengthFactor = averageStrength / 100; // Normalize to 0-1
      const confluenceFactor = dominantPatterns / totalPatterns;
      confidence = (strengthFactor * 0.7 + confluenceFactor * 0.3) * 100;
    }

    return {
      bullish_patterns,
      bearish_patterns,
      neutral_patterns,
      overall_sentiment,
      confidence: Math.min(100, Math.max(0, confidence)),
    };
  }

  /**
   * Analyze market context for patterns
   */
  private analyzeContext(
    patternResult: PatternDetectionResult,
    marketContext: MarketContext,
    priceHistory: CandlestickData[]
  ): PatternAnalysis['context'] {
    const currentPrice = patternResult.candle.close;

    // Analyze trend alignment
    let trend_alignment: 'with_trend' | 'against_trend' | 'neutral' = 'neutral';
    const patternSentiment = this.getPatternSentiment(patternResult.patterns);
    
    if (patternSentiment === 'bullish' && marketContext.trend === 'bullish') {
      trend_alignment = 'with_trend';
    } else if (patternSentiment === 'bearish' && marketContext.trend === 'bearish') {
      trend_alignment = 'with_trend';
    } else if (
      (patternSentiment === 'bullish' && marketContext.trend === 'bearish') ||
      (patternSentiment === 'bearish' && marketContext.trend === 'bullish')
    ) {
      trend_alignment = 'against_trend';
    }

    // Analyze support/resistance levels
    let support_resistance: 'at_support' | 'at_resistance' | 'neutral' = 'neutral';
    if (marketContext.support_level && Math.abs(currentPrice - marketContext.support_level) / currentPrice < 0.01) {
      support_resistance = 'at_support';
    } else if (marketContext.resistance_level && Math.abs(currentPrice - marketContext.resistance_level) / currentPrice < 0.01) {
      support_resistance = 'at_resistance';
    }

    // Analyze volume confirmation
    const volume_confirmation = this.analyzeVolumeConfirmation(patternResult, marketContext);

    // Determine pattern reliability
    const pattern_reliability = this.calculatePatternReliability(
      patternResult.patterns,
      trend_alignment,
      support_resistance,
      volume_confirmation
    );

    return {
      trend_alignment,
      support_resistance,
      volume_confirmation,
      pattern_reliability,
    };
  }

  /**
   * Analyze trading signals
   */
  private analyzeSignals(
    analysis: PatternAnalysis,
    marketContext: MarketContext
  ): PatternAnalysis['signals'] {
    let entry_signal = false;
    let reversal_probability = 0;
    let continuation_probability = 0;
    let risk_level: 'low' | 'medium' | 'high' = 'medium';

    // Calculate reversal probability
    const reversalPatterns = ['Hammer', 'Shooting Star', 'Doji', 'Engulfing', 'Morning Star', 'Evening Star'];
    const hasReversalPattern = analysis.patterns.some(p => 
      reversalPatterns.some(rp => p.name.includes(rp))
    );

    if (hasReversalPattern) {
      reversal_probability = analysis.confluence.confidence;
      
      // Increase probability if against trend (reversal more likely)
      if (analysis.context.trend_alignment === 'against_trend') {
        reversal_probability = Math.min(100, reversal_probability * 1.2);
      }
      
      // Increase probability at support/resistance
      if (analysis.context.support_resistance !== 'neutral') {
        reversal_probability = Math.min(100, reversal_probability * 1.15);
      }
    }

    // Calculate continuation probability
    const continuationPatterns = ['Marubozu', 'Three White Soldiers', 'Three Black Crows'];
    const hasContinuationPattern = analysis.patterns.some(p => 
      continuationPatterns.some(cp => p.name.includes(cp))
    );

    if (hasContinuationPattern) {
      continuation_probability = analysis.confluence.confidence;
      
      // Increase probability if with trend
      if (analysis.context.trend_alignment === 'with_trend') {
        continuation_probability = Math.min(100, continuation_probability * 1.2);
      }
    }

    // Determine entry signal
    entry_signal = (
      analysis.confluence.confidence > 70 &&
      analysis.context.pattern_reliability !== 'low' &&
      (reversal_probability > 75 || continuation_probability > 75)
    );

    // Determine risk level
    if (analysis.context.pattern_reliability === 'high' && analysis.confluence.confidence > 80) {
      risk_level = 'low';
    } else if (analysis.context.pattern_reliability === 'low' || analysis.confluence.confidence < 50) {
      risk_level = 'high';
    }

    // Adjust risk based on volatility
    if (marketContext.volatility === 'high') {
      risk_level = risk_level === 'low' ? 'medium' : 'high';
    }

    return {
      entry_signal,
      reversal_probability,
      continuation_probability,
      risk_level,
    };
  }

  /**
   * Get overall sentiment from patterns
   */
  private getPatternSentiment(patterns: CandlestickPattern[]): 'bullish' | 'bearish' | 'neutral' {
    const bullish = patterns.filter(p => p.type === 'bullish').length;
    const bearish = patterns.filter(p => p.type === 'bearish').length;

    if (bullish > bearish) return 'bullish';
    if (bearish > bullish) return 'bearish';
    return 'neutral';
  }

  /**
   * Analyze volume confirmation
   */
  private analyzeVolumeConfirmation(
    patternResult: PatternDetectionResult,
    marketContext: MarketContext
  ): boolean {
    if (!marketContext.volume_average || !patternResult.candle.volume) {
      return false; // Cannot confirm without volume data
    }

    // Volume should be above average for strong patterns
    return patternResult.candle.volume > marketContext.volume_average * 1.2;
  }

  /**
   * Calculate pattern reliability
   */
  private calculatePatternReliability(
    patterns: CandlestickPattern[],
    trend_alignment: string,
    support_resistance: string,
    volume_confirmation: boolean
  ): 'high' | 'medium' | 'low' {
    let score = 0;

    // Base score from pattern strength
    const averageStrength = patterns.reduce((sum, p) => sum + p.strength, 0) / patterns.length;
    score += averageStrength * 0.4;

    // Trend alignment bonus
    if (trend_alignment === 'with_trend') {
      score += 20;
    } else if (trend_alignment === 'against_trend') {
      score += 10; // Reversal patterns can be strong against trend
    }

    // Support/resistance bonus
    if (support_resistance !== 'neutral') {
      score += 15;
    }

    // Volume confirmation bonus
    if (volume_confirmation) {
      score += 15;
    }

    // Multiple patterns bonus
    if (patterns.length > 1) {
      score += 10;
    }

    if (score >= 80) return 'high';
    if (score >= 60) return 'medium';
    return 'low';
  }

  /**
   * Store analysis in history
   */
  private storeAnalysis(symbol: string, analysis: PatternAnalysis): void {
    if (!this.analysisHistory.has(symbol)) {
      this.analysisHistory.set(symbol, []);
    }

    const history = this.analysisHistory.get(symbol)!;
    history.push(analysis);

    // Maintain max history length
    if (history.length > this.maxHistoryLength) {
      history.shift();
    }
  }

  /**
   * Get analysis history for a symbol
   */
  public getAnalysisHistory(symbol: string, limit?: number): PatternAnalysis[] {
    const history = this.analysisHistory.get(symbol) || [];
    return limit ? history.slice(-limit) : [...history];
  }

  /**
   * Get latest analysis for a symbol
   */
  public getLatestAnalysis(symbol: string): PatternAnalysis | null {
    const history = this.analysisHistory.get(symbol);
    return history && history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * Get pattern statistics for a symbol
   */
  public getPatternStatistics(symbol: string, timeframe: number = 86400000): any {
    const history = this.analysisHistory.get(symbol) || [];
    const cutoffTime = Date.now() - timeframe;
    const recentAnalyses = history.filter(a => a.timestamp > cutoffTime);

    const stats = {
      total_patterns: 0,
      bullish_patterns: 0,
      bearish_patterns: 0,
      neutral_patterns: 0,
      high_confidence_patterns: 0,
      entry_signals: 0,
      average_confidence: 0,
    };

    recentAnalyses.forEach(analysis => {
      stats.total_patterns += analysis.patterns.length;
      stats.bullish_patterns += analysis.confluence.bullish_patterns;
      stats.bearish_patterns += analysis.confluence.bearish_patterns;
      stats.neutral_patterns += analysis.confluence.neutral_patterns;
      
      if (analysis.confluence.confidence > 80) {
        stats.high_confidence_patterns++;
      }
      
      if (analysis.signals.entry_signal) {
        stats.entry_signals++;
      }
      
      stats.average_confidence += analysis.confluence.confidence;
    });

    if (recentAnalyses.length > 0) {
      stats.average_confidence /= recentAnalyses.length;
    }

    return stats;
  }

  /**
   * Clear analysis history for a symbol
   */
  public clearHistory(symbol: string): void {
    this.analysisHistory.delete(symbol);
    logger.patterns('info', `Cleared pattern analysis history for ${symbol}`);
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.analysisHistory.keys());
  }
}

// Export singleton instance
export const patternAnalyzer = new PatternAnalyzer();
