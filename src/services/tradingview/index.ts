export { tradingViewDataFetcher, TradingViewQuote, TradingViewHistoricalData } from './data-fetcher';
export { tradingViewMarketDataManager, PriceUpdateEvent } from './market-data-manager';

// Re-export for convenience
export const tradingViewService = {
  dataFetcher: () => import('./data-fetcher').then(m => m.tradingViewDataFetcher),
  marketDataManager: () => import('./market-data-manager').then(m => m.tradingViewMarketDataManager),
};
