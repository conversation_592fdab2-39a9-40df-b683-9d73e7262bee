import { EventEmitter } from 'events';
import { config } from '../../config';
import { CandlestickData } from '../../types';
import { logger } from '../../utils/logger';

export interface TradingViewQuote {
  symbol: string;
  price: number;
  bid: number;
  ask: number;
  spread: number;
  timestamp: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface TradingViewHistoricalData {
  symbol: string;
  timeframe: string;
  data: Array<{
    time: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>;
}

export class TradingViewDataFetcher extends EventEmitter {
  private updateInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastPrice = 0;
  private priceDirection = 1; // 1 for up, -1 for down

  constructor() {
    super();
  }

  /**
   * Start fetching real-time data
   */
  public start(): void {
    if (this.isRunning) {
      logger.trading('warn', 'TradingView data fetcher is already running');
      return;
    }

    this.isRunning = true;
    logger.trading('info', 'Starting TradingView data fetcher', {
      symbol: config.tradingView.symbol,
      interval: config.tradingView.updateInterval
    });

    // Initialize with a base price for EUR/USD
    this.lastPrice = 1.105;

    // Start the update interval
    this.updateInterval = setInterval(() => {
      this.generateRealtimeData();
    }, config.tradingView.updateInterval);

    // Emit initial data
    this.generateRealtimeData();
  }

  /**
   * Stop fetching data
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    logger.trading('info', 'TradingView data fetcher stopped');
  }

  /**
   * Generate realistic forex price movements
   */
  private generateRealtimeData(): void {
    try {
      // Generate realistic price movement
      const volatility = 0.0001; // 1 pip for EUR/USD
      const randomChange = (Math.random() - 0.5) * 2 * volatility;

      // Add some trend bias (70% chance to continue current direction)
      const trendBias =
        Math.random() < 0.7 ? this.priceDirection : -this.priceDirection;
      const biasedChange = randomChange + trendBias * volatility * 0.3;

      // Update price
      this.lastPrice += biasedChange;

      // Ensure price stays within reasonable bounds for EUR/USD
      this.lastPrice = Math.max(1.05, Math.min(1.15, this.lastPrice));

      // Update direction based on price change
      if (biasedChange > 0) {
        this.priceDirection = 1;
      } else if (biasedChange < 0) {
        this.priceDirection = -1;
      }

      // Generate bid/ask spread (typically 1-2 pips for EUR/USD)
      const spread = 0.00015 + Math.random() * 0.00005; // 1.5-2 pips
      const bid = this.lastPrice - spread / 2;
      const ask = this.lastPrice + spread / 2;

      // Generate volume (random but realistic)
      const volume = Math.floor(Math.random() * 1000) + 100;

      // Calculate change from previous
      const change = biasedChange;
      const changePercent = (change / this.lastPrice) * 100;

      const quote: TradingViewQuote = {
        symbol: config.tradingView.symbol,
        price: this.lastPrice,
        bid: bid,
        ask: ask,
        spread: spread,
        timestamp: Date.now(),
        volume: volume,
        change: change,
        changePercent: changePercent
      };

      // Emit the quote data
      this.emit('quote', quote);

      logger.trading(
        'debug',
        `Generated quote for ${config.tradingView.symbol}`,
        {
          price: this.lastPrice.toFixed(5),
          bid: bid.toFixed(5),
          ask: ask.toFixed(5),
          spread: (spread * 100000).toFixed(1) + ' pips',
          change: (change * 100000).toFixed(1) + ' pips'
        }
      );
    } catch (error) {
      logger.trading('error', 'Error generating realtime data', { error });
    }
  }

  /**
   * Get historical data (simulated)
   */
  public async getHistoricalData(
    symbol: string,
    timeframe: string = '1m',
    count: number = 100
  ): Promise<CandlestickData[]> {
    try {
      logger.trading('info', `Fetching historical data for ${symbol}`, {
        timeframe,
        count
      });

      // Generate historical candles
      const candles: CandlestickData[] = [];
      const now = Date.now();
      const intervalMs = 60000; // 1 minute

      let basePrice = 1.105; // Starting price for EUR/USD

      for (let i = count - 1; i >= 0; i--) {
        const timestamp = now - i * intervalMs;

        // Generate OHLC data with realistic movements
        const volatility = 0.0002; // 2 pips max movement per candle
        const open = basePrice;

        // Generate high and low
        const range = Math.random() * volatility;
        const high = open + range * (0.3 + Math.random() * 0.7);
        const low = open - range * (0.3 + Math.random() * 0.7);

        // Generate close (with slight trend)
        const trendBias = (Math.random() - 0.5) * volatility * 0.5;
        const close = open + trendBias;

        // Ensure OHLC relationships are correct
        const actualHigh = Math.max(open, close, high);
        const actualLow = Math.min(open, close, low);
        const actualClose = Math.max(actualLow, Math.min(actualHigh, close));

        const volume = Math.floor(Math.random() * 500) + 50;

        candles.push({
          timestamp,
          open,
          high: actualHigh,
          low: actualLow,
          close: actualClose,
          volume
        });

        // Update base price for next candle
        basePrice = actualClose;
      }

      logger.trading(
        'info',
        `Generated ${candles.length} historical candles for ${symbol}`
      );
      return candles;
    } catch (error) {
      logger.trading('error', `Failed to fetch historical data for ${symbol}`, {
        error
      });
      throw error;
    }
  }

  /**
   * Get current quote
   */
  public getCurrentQuote(): TradingViewQuote | null {
    if (!this.isRunning) {
      return null;
    }

    const spread = 0.00015;
    const bid = this.lastPrice - spread / 2;
    const ask = this.lastPrice + spread / 2;

    return {
      symbol: config.tradingView.symbol,
      price: this.lastPrice,
      bid: bid,
      ask: ask,
      spread: spread,
      timestamp: Date.now(),
      volume: Math.floor(Math.random() * 1000) + 100,
      change: 0,
      changePercent: 0
    };
  }

  /**
   * Check if data fetcher is running
   */
  public isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Get supported symbols (for future expansion)
   */
  public getSupportedSymbols(): string[] {
    return [
      'EURUSD',
      'GBPUSD',
      'USDJPY',
      'USDCHF',
      'AUDUSD',
      'USDCAD',
      'NZDUSD'
    ];
  }

  /**
   * Test connection (always returns true for simulated data)
   */
  public async testConnection(): Promise<boolean> {
    logger.trading(
      'info',
      'TradingView data fetcher connection test successful (simulated data)'
    );
    return true;
  }
}

// Export singleton instance
export const tradingViewDataFetcher = new TradingViewDataFetcher();
