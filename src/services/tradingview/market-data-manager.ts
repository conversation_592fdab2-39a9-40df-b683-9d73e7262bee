import { EventEmitter } from 'events';
import { config } from '../../config';
import { CandlestickData, MarketData } from '../../types';
import { logger } from '../../utils/logger';
import { tradingViewDataFetcher, TradingViewQuote } from './data-fetcher';

export interface PriceUpdateEvent {
  symbol: string;
  price: number;
  bid: number;
  ask: number;
  spread: number;
  timestamp: number;
  candlestick: CandlestickData;
}

export class TradingViewMarketDataManager extends EventEmitter {
  private marketData: Map<string, MarketData> = new Map();
  private candleBuffers: Map<string, CandlestickData[]> = new Map();
  private lastCandleTime: Map<string, number> = new Map();
  private readonly maxCandleHistory = 200; // Keep last 200 candles for indicators

  constructor() {
    super();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Listen to TradingView data fetcher events
    tradingViewDataFetcher.on('quote', (quote: TradingViewQuote) => {
      this.handleQuoteUpdate(quote);
    });
  }

  /**
   * Initialize market data for a symbol
   */
  public async initializeSymbol(symbol: string): Promise<void> {
    try {
      logger.trading('info', `Initializing market data for ${symbol}`);

      // Get historical data from TradingView data fetcher
      const historicalCandles = await tradingViewDataFetcher.getHistoricalData(
        symbol,
        '1m',
        this.maxCandleHistory
      );

      // Initialize market data structure
      const marketData: MarketData = {
        symbol,
        candles: historicalCandles,
        indicators: {
          rsi: 0,
          ema: 0,
          macd: { macd: 0, signal: 0, histogram: 0 },
          bollingerBands: { upper: 0, middle: 0, lower: 0 },
        },
        patterns: [],
        lastUpdate: Date.now(),
      };

      this.marketData.set(symbol, marketData);
      this.candleBuffers.set(symbol, [...historicalCandles]);

      if (historicalCandles.length > 0) {
        this.lastCandleTime.set(symbol, historicalCandles[historicalCandles.length - 1].timestamp);
      }

      logger.trading('info', `Market data initialized for ${symbol}`, {
        candleCount: historicalCandles.length,
        latestTimestamp: historicalCandles[historicalCandles.length - 1]?.timestamp,
      });

      // Emit initialization complete event
      this.emit('symbolInitialized', { symbol, marketData });

    } catch (error) {
      logger.trading('error', `Failed to initialize market data for ${symbol}`, { error });
      throw error;
    }
  }

  /**
   * Handle real-time quote updates from TradingView
   */
  private handleQuoteUpdate(quote: TradingViewQuote): void {
    const { symbol, price, bid, ask, spread, timestamp } = quote;

    // Get current market data
    const marketData = this.marketData.get(symbol);
    if (!marketData) {
      logger.trading('warn', `Received quote update for uninitialized symbol: ${symbol}`);
      return;
    }

    // Update current candle or create new one
    this.updateCandleData(symbol, price, timestamp);

    // Update market data
    marketData.lastUpdate = timestamp;

    // Create candlestick data for the event
    const candlestick: CandlestickData = {
      timestamp,
      open: price,
      high: price,
      low: price,
      close: price,
      volume: quote.volume || 0,
    };

    // Emit price update event
    this.emit('priceUpdate', {
      symbol,
      price,
      bid,
      ask,
      spread,
      timestamp,
      candlestick,
      marketData: this.getMarketData(symbol),
    });

    logger.trading('debug', `Price update processed for ${symbol}`, {
      price: price.toFixed(5),
      bid: bid.toFixed(5),
      ask: ask.toFixed(5),
      spread: (spread * 100000).toFixed(1) + ' pips',
      timestamp: new Date(timestamp).toISOString(),
    });
  }

  /**
   * Update candle data with new price
   */
  private updateCandleData(symbol: string, price: number, timestamp: number): void {
    const candleBuffer = this.candleBuffers.get(symbol);
    if (!candleBuffer) return;

    const lastCandleTime = this.lastCandleTime.get(symbol) || 0;
    const candleInterval = 60000; // 1 minute in milliseconds

    // Check if we need to create a new candle
    const shouldCreateNewCandle = timestamp - lastCandleTime >= candleInterval;

    if (shouldCreateNewCandle || candleBuffer.length === 0) {
      // Create new candle
      const newCandle: CandlestickData = {
        timestamp,
        open: price,
        high: price,
        low: price,
        close: price,
        volume: 0,
      };

      candleBuffer.push(newCandle);
      this.lastCandleTime.set(symbol, timestamp);

      // Maintain buffer size
      if (candleBuffer.length > this.maxCandleHistory) {
        candleBuffer.shift();
      }

      // Update market data candles
      const marketData = this.marketData.get(symbol);
      if (marketData) {
        marketData.candles = [...candleBuffer];
      }

      // Calculate technical indicators
      this.calculateAndUpdateIndicators(symbol, candleBuffer);

      logger.trading('debug', `New candle created for ${symbol}`, {
        timestamp: new Date(timestamp).toISOString(),
        price: price.toFixed(5),
      });

      // Emit new candle event
      this.emit('newCandle', {
        symbol,
        candle: newCandle,
        marketData: this.getMarketData(symbol),
      });

    } else {
      // Update current candle
      const currentCandle = candleBuffer[candleBuffer.length - 1];
      if (currentCandle) {
        currentCandle.high = Math.max(currentCandle.high, price);
        currentCandle.low = Math.min(currentCandle.low, price);
        currentCandle.close = price;

        // Update market data
        const marketData = this.marketData.get(symbol);
        if (marketData) {
          marketData.candles = [...candleBuffer];
        }
      }
    }
  }

  /**
   * Get market data for a symbol
   */
  public getMarketData(symbol: string): MarketData | null {
    return this.marketData.get(symbol) || null;
  }

  /**
   * Get candle buffer for a symbol
   */
  public getCandleBuffer(symbol: string): CandlestickData[] {
    return this.candleBuffers.get(symbol) || [];
  }

  /**
   * Get latest candle for a symbol
   */
  public getLatestCandle(symbol: string): CandlestickData | null {
    const buffer = this.candleBuffers.get(symbol);
    return buffer && buffer.length > 0 ? buffer[buffer.length - 1] : null;
  }

  /**
   * Get latest price for a symbol
   */
  public getLatestPrice(symbol: string): number | null {
    const latestCandle = this.getLatestCandle(symbol);
    return latestCandle ? latestCandle.close : null;
  }

  /**
   * Update indicators for a symbol
   */
  public updateIndicators(symbol: string, indicators: any): void {
    const marketData = this.marketData.get(symbol);
    if (marketData) {
      marketData.indicators = indicators;
      this.emit('indicatorsUpdated', { symbol, indicators, marketData });
    }
  }

  /**
   * Update patterns for a symbol
   */
  public updatePatterns(symbol: string, patterns: any[]): void {
    const marketData = this.marketData.get(symbol);
    if (marketData) {
      marketData.patterns = patterns;
      this.emit('patternsUpdated', { symbol, patterns, marketData });
    }
  }

  /**
   * Start market data manager
   */
  public async start(): Promise<void> {
    try {
      // Test TradingView data fetcher connection
      const connected = await tradingViewDataFetcher.testConnection();
      if (!connected) {
        throw new Error('Failed to connect to TradingView data source');
      }

      // Start the data fetcher
      tradingViewDataFetcher.start();

      // Initialize default symbol
      await this.initializeSymbol(config.tradingView.symbol);

      logger.trading('info', 'TradingView market data manager started successfully');
    } catch (error) {
      logger.trading('error', 'Failed to start TradingView market data manager', { error });
      throw error;
    }
  }

  /**
   * Stop market data manager
   */
  public async stop(): Promise<void> {
    try {
      tradingViewDataFetcher.stop();
      this.marketData.clear();
      this.candleBuffers.clear();
      this.lastCandleTime.clear();
      logger.trading('info', 'TradingView market data manager stopped');
    } catch (error) {
      logger.trading('error', 'Error stopping TradingView market data manager', { error });
    }
  }

  /**
   * Get connection status
   */
  public isConnected(): boolean {
    return tradingViewDataFetcher.isActive();
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.marketData.keys());
  }

  /**
   * Calculate and update technical indicators for a symbol
   */
  private async calculateAndUpdateIndicators(symbol: string, candles: CandlestickData[]): Promise<void> {
    try {
      // Import indicators and patterns dynamically
      const { technicalIndicatorsEngine } = await import('../indicators');
      const { indicatorAnalyzer } = await import('../indicators/analyzer');
      const { candlestickPatternEngine } = await import('../patterns');
      const { patternAnalyzer } = await import('../patterns/analyzer');

      // Calculate indicators
      const indicatorResult = technicalIndicatorsEngine.updateData(symbol, candles);
      
      if (indicatorResult) {
        // Update market data with indicators
        this.updateIndicators(symbol, indicatorResult.indicators);

        // Perform advanced analysis
        const previousAnalysis = indicatorAnalyzer.getLatestAnalysis(symbol);
        const analysis = indicatorAnalyzer.analyzeIndicators(
          symbol,
          indicatorResult,
          candles,
          previousAnalysis || undefined
        );

        // Detect candlestick patterns
        const patternResult = candlestickPatternEngine.detectPatterns(symbol, candles);
        
        // Analyze patterns with market context
        const marketContext = {
          trend: analysis.confluence.overall_bias === 'bullish' ? 'bullish' as const : 
                 analysis.confluence.overall_bias === 'bearish' ? 'bearish' as const : 'sideways' as const,
          volatility: 'medium' as const, // Could be calculated from ATR
        };
        
        const patternAnalysis = patternAnalyzer.analyzePatterns(
          patternResult,
          marketContext,
          candles
        );

        // Update market data with patterns
        this.updatePatterns(symbol, patternResult.patterns);

        // Emit comprehensive analysis event
        const marketAnalysisData = {
          symbol,
          indicatorAnalysis: analysis,
          patternAnalysis,
          indicatorResult,
          patternResult,
          marketData: this.getMarketData(symbol)
        };
        
        this.emit('marketAnalysis', marketAnalysisData);

        // Send to signal manager for signal generation
        const { signalManager } = await import('../signals/manager');
        await signalManager.processMarketAnalysis(marketAnalysisData);

        logger.trading('debug', `Market analysis complete for ${symbol}`, {
          rsi: indicatorResult.indicators.rsi,
          ema: indicatorResult.indicators.ema,
          indicatorConfidence: analysis.confluence.confidence,
          indicatorBias: analysis.confluence.overall_bias,
          patterns: patternResult.patterns.length,
          patternConfidence: patternAnalysis.confluence.confidence,
          patternSentiment: patternAnalysis.confluence.overall_sentiment
        });
      }
    } catch (error) {
      logger.trading('error', `Failed to calculate indicators for ${symbol}`, { error });
    }
  }
}

// Export singleton instance
export const tradingViewMarketDataManager = new TradingViewMarketDataManager();
