import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../../config';
import {
  AdvancedSignal,
  CandlestickData,
  CandlestickPattern,
  SignalAnalysis,
  TechnicalIndicators
} from '../../types';
import { logger } from '../../utils/logger';

export interface SignalGenerationInput {
  symbol: string;
  timestamp: number;
  price: number;
  candles: CandlestickData[];
  indicators: TechnicalIndicators;
  patterns: CandlestickPattern[];
  indicatorAnalysis: any;
  patternAnalysis: any;
}

export interface SignalConfig {
  minConfidence: number;
  maxSignalsPerDay: number;
  riskRewardRatio: number;
  stopLossPips: number;
  takeProfitPips: number;
  enablePriceAction: boolean;
  enableICT: boolean;
  enableSMC: boolean;
  enableElliottWave: boolean;
  enableWyckoff: boolean;
}

export class AdvancedSignalEngine extends EventEmitter {
  private signalConfig: SignalConfig;
  private dailySignalCount: Map<string, number> = new Map();
  private lastSignalTime: Map<string, number> = new Map();
  private signalHistory: Map<string, AdvancedSignal[]> = new Map();
  private readonly maxHistoryLength = 100;
  private readonly minSignalInterval = 30000; // 30 seconds between signals for testing

  constructor() {
    super();
    this.signalConfig = this.initializeConfig();
    this.resetDailyCounters();
  }

  private initializeConfig(): SignalConfig {
    return {
      minConfidence: config.trading.minConfidenceLevel,
      maxSignalsPerDay: config.trading.maxSignalsPerDay,
      riskRewardRatio: config.trading.riskRewardRatio,
      stopLossPips: config.trading.stopLossPips,
      takeProfitPips: config.trading.takeProfitPips,
      enablePriceAction: true,
      enableICT: true,
      enableSMC: true,
      enableElliottWave: true,
      enableWyckoff: true
    };
  }

  /**
   * Generate trading signal from comprehensive analysis
   */
  public async generateSignal(
    input: SignalGenerationInput
  ): Promise<AdvancedSignal | null> {
    try {
      const {
        symbol,
        timestamp,
        price,
        candles,
        indicators,
        patterns,
        indicatorAnalysis,
        patternAnalysis
      } = input;

      // Check if we can generate a signal
      if (!this.canGenerateSignal(symbol, timestamp)) {
        return null;
      }

      // Perform comprehensive analysis
      const analysis = await this.performAdvancedAnalysis(input);

      // Calculate signal confidence
      const confidence = this.calculateOverallConfidence(
        analysis,
        indicatorAnalysis,
        patternAnalysis
      );

      // Check minimum confidence threshold
      if (confidence < this.signalConfig.minConfidence) {
        logger.trading('debug', `Signal confidence too low for ${symbol}`, {
          confidence,
          required: this.signalConfig.minConfidence
        });
        return null;
      }

      // Determine signal direction
      const signalType = this.determineSignalDirection(
        analysis,
        indicatorAnalysis,
        patternAnalysis
      );
      if (!signalType) {
        return null;
      }

      // Calculate entry, stop loss, and take profit levels
      const levels = this.calculateTradingLevels(price, signalType, analysis);

      // Create the signal
      const signal: AdvancedSignal = {
        id: uuidv4(),
        symbol,
        timestamp,
        type: signalType,
        entryPrice: levels.entry,
        stopLoss: levels.stopLoss,
        takeProfit: levels.takeProfit,
        riskRewardRatio: levels.riskReward,
        confidence,
        indicators,
        patterns,
        reasoning: this.generateReasoning(
          analysis,
          indicatorAnalysis,
          patternAnalysis
        ),
        status: 'pending',
        analysis,
        multiTimeframe: {
          '1m': indicators // Current timeframe
        }
      };

      // Store signal
      this.storeSignal(symbol, signal);
      this.updateSignalCounters(symbol, timestamp);

      // Emit signal event
      this.emit('signalGenerated', signal);

      logger.trading('info', `Trading signal generated for ${symbol}`, {
        type: signalType,
        confidence,
        entry: levels.entry,
        stopLoss: levels.stopLoss,
        takeProfit: levels.takeProfit
      });

      return signal;
    } catch (error) {
      logger.trading('error', `Failed to generate signal for ${input.symbol}`, {
        error
      });
      return null;
    }
  }

  /**
   * Perform advanced analysis combining multiple methodologies
   */
  private async performAdvancedAnalysis(
    input: SignalGenerationInput
  ): Promise<SignalAnalysis> {
    const { candles, indicators, patterns, price } = input;

    const analysis: SignalAnalysis = {
      priceAction: await this.analyzePriceAction(candles, price),
      ict: await this.analyzeICT(candles, indicators),
      smc: await this.analyzeSMC(candles, indicators),
      elliottWave: await this.analyzeElliottWave(candles),
      wyckoff: await this.analyzeWyckoff(candles, indicators)
    };

    return analysis;
  }

  /**
   * Price Action Analysis
   */
  private async analyzePriceAction(
    candles: CandlestickData[],
    currentPrice: number
  ): Promise<SignalAnalysis['priceAction']> {
    if (!this.signalConfig.enablePriceAction || candles.length < 20) {
      return {
        trend: 'sideways',
        strength: 0,
        support: 0,
        resistance: 0
      };
    }

    // Calculate trend using price structure
    const highs = candles.slice(-10).map((c) => c.high);
    const lows = candles.slice(-10).map((c) => c.low);

    const recentHighs = highs.slice(-5);
    const recentLows = lows.slice(-5);
    const olderHighs = highs.slice(0, 5);
    const olderLows = lows.slice(0, 5);

    const avgRecentHigh =
      recentHighs.reduce((a, b) => a + b, 0) / recentHighs.length;
    const avgRecentLow =
      recentLows.reduce((a, b) => a + b, 0) / recentLows.length;
    const avgOlderHigh =
      olderHighs.reduce((a, b) => a + b, 0) / olderHighs.length;
    const avgOlderLow = olderLows.reduce((a, b) => a + b, 0) / olderLows.length;

    let trend: 'bullish' | 'bearish' | 'sideways' = 'sideways';
    let strength = 0;

    // Higher highs and higher lows = bullish
    if (avgRecentHigh > avgOlderHigh && avgRecentLow > avgOlderLow) {
      trend = 'bullish';
      strength = Math.min(
        100,
        ((avgRecentHigh - avgOlderHigh) / avgOlderHigh) * 1000
      );
    }
    // Lower highs and lower lows = bearish
    else if (avgRecentHigh < avgOlderHigh && avgRecentLow < avgOlderLow) {
      trend = 'bearish';
      strength = Math.min(
        100,
        ((avgOlderHigh - avgRecentHigh) / avgOlderHigh) * 1000
      );
    }

    // Find support and resistance levels
    const support = Math.min(...lows);
    const resistance = Math.max(...highs);

    return {
      trend,
      strength,
      support,
      resistance
    };
  }

  /**
   * ICT (Inner Circle Trader) Analysis
   */
  private async analyzeICT(
    candles: CandlestickData[],
    indicators: TechnicalIndicators
  ): Promise<SignalAnalysis['ict']> {
    if (!this.signalConfig.enableICT || candles.length < 50) {
      return {
        orderBlock: false,
        fairValueGap: false,
        liquidityPool: false
      };
    }

    // Order Block Detection (simplified)
    const orderBlock = this.detectOrderBlock(candles);

    // Fair Value Gap Detection
    const fairValueGap = this.detectFairValueGap(candles);

    // Liquidity Pool Detection
    const liquidityPool = this.detectLiquidityPool(candles);

    return {
      orderBlock,
      fairValueGap,
      liquidityPool
    };
  }

  /**
   * SMC (Smart Money Concepts) Analysis
   */
  private async analyzeSMC(
    candles: CandlestickData[],
    indicators: TechnicalIndicators
  ): Promise<SignalAnalysis['smc']> {
    if (!this.signalConfig.enableSMC || candles.length < 30) {
      return {
        changeOfCharacter: false,
        breakOfStructure: false,
        orderFlow: 'neutral'
      };
    }

    // Change of Character (CHoCH)
    const changeOfCharacter = this.detectChangeOfCharacter(candles);

    // Break of Structure (BOS)
    const breakOfStructure = this.detectBreakOfStructure(candles);

    // Order Flow Analysis
    const orderFlow = this.analyzeOrderFlow(candles);

    return {
      changeOfCharacter,
      breakOfStructure,
      orderFlow
    };
  }

  /**
   * Elliott Wave Analysis (simplified)
   */
  private async analyzeElliottWave(
    candles: CandlestickData[]
  ): Promise<SignalAnalysis['elliottWave']> {
    if (!this.signalConfig.enableElliottWave || candles.length < 50) {
      return {
        wave: 'unknown',
        degree: 'unknown',
        direction: 'corrective'
      };
    }

    // Simplified Elliott Wave detection
    const waveAnalysis = this.detectElliottWavePattern(candles);

    return waveAnalysis;
  }

  /**
   * Wyckoff Analysis
   */
  private async analyzeWyckoff(
    candles: CandlestickData[],
    indicators: TechnicalIndicators
  ): Promise<SignalAnalysis['wyckoff']> {
    if (!this.signalConfig.enableWyckoff || candles.length < 100) {
      return {
        phase: 'accumulation',
        position: 'unknown'
      };
    }

    // Wyckoff market cycle analysis
    const wyckoffAnalysis = this.analyzeWyckoffCycle(candles, indicators);

    return wyckoffAnalysis;
  }

  /**
   * Helper methods for specific analysis techniques
   */
  private detectOrderBlock(candles: CandlestickData[]): boolean {
    // Simplified order block detection
    // Look for strong rejection candles followed by retest
    const recent = candles.slice(-10);
    return recent.some((candle, i) => {
      if (i < 2) return false;
      const prev = recent[i - 1];
      const body = Math.abs(candle.close - candle.open);
      const range = candle.high - candle.low;
      return (
        body / range > 0.7 &&
        Math.abs(candle.close - prev.close) / prev.close > 0.005
      );
    });
  }

  private detectFairValueGap(candles: CandlestickData[]): boolean {
    // Look for gaps in price action
    if (candles.length < 3) return false;
    const recent = candles.slice(-3);
    const gap1 = recent[0].high < recent[2].low;
    const gap2 = recent[0].low > recent[2].high;
    return gap1 || gap2;
  }

  private detectLiquidityPool(candles: CandlestickData[]): boolean {
    // Look for equal highs/lows that could be liquidity pools
    const highs = candles.slice(-20).map((c) => c.high);
    const lows = candles.slice(-20).map((c) => c.low);

    // Check for equal highs
    const equalHighs = highs.filter((h, i) =>
      highs.some((h2, j) => j !== i && Math.abs(h - h2) / h < 0.001)
    );

    // Check for equal lows
    const equalLows = lows.filter((l, i) =>
      lows.some((l2, j) => j !== i && Math.abs(l - l2) / l < 0.001)
    );

    return equalHighs.length >= 2 || equalLows.length >= 2;
  }

  private detectChangeOfCharacter(candles: CandlestickData[]): boolean {
    // Simplified CHoCH detection - trend change confirmation
    if (candles.length < 10) return false;

    const recent = candles.slice(-10);
    const older = candles.slice(-20, -10);

    const recentTrend =
      recent[recent.length - 1].close > recent[0].close ? 'bullish' : 'bearish';
    const olderTrend =
      older[older.length - 1].close > older[0].close ? 'bullish' : 'bearish';

    return recentTrend !== olderTrend;
  }

  private detectBreakOfStructure(candles: CandlestickData[]): boolean {
    // Look for breaks of previous swing highs/lows
    if (candles.length < 15) return false;

    const recent = candles.slice(-5);
    const previous = candles.slice(-15, -5);

    const recentHigh = Math.max(...recent.map((c) => c.high));
    const recentLow = Math.min(...recent.map((c) => c.low));
    const prevHigh = Math.max(...previous.map((c) => c.high));
    const prevLow = Math.min(...previous.map((c) => c.low));

    return recentHigh > prevHigh || recentLow < prevLow;
  }

  private analyzeOrderFlow(
    candles: CandlestickData[]
  ): 'bullish' | 'bearish' | 'neutral' {
    // Simplified order flow based on candle closes vs opens
    const recent = candles.slice(-10);
    const bullishCandles = recent.filter((c) => c.close > c.open).length;
    const bearishCandles = recent.filter((c) => c.close < c.open).length;

    if (bullishCandles > bearishCandles * 1.5) return 'bullish';
    if (bearishCandles > bullishCandles * 1.5) return 'bearish';
    return 'neutral';
  }

  private detectElliottWavePattern(
    candles: CandlestickData[]
  ): SignalAnalysis['elliottWave'] {
    // Very simplified Elliott Wave detection
    // In a real implementation, this would be much more sophisticated
    const trend =
      candles[candles.length - 1].close > candles[0].close
        ? 'impulse'
        : 'corrective';

    return {
      wave: 'wave-3', // Simplified
      degree: 'minor',
      direction: trend
    };
  }

  private analyzeWyckoffCycle(
    candles: CandlestickData[],
    indicators: TechnicalIndicators
  ): SignalAnalysis['wyckoff'] {
    // Simplified Wyckoff analysis based on volume and price action
    // This would be much more complex in a real implementation

    const recentVolumes = candles.slice(-20).map((c) => c.volume || 0);
    const avgVolume =
      recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
    const currentVolume = candles[candles.length - 1].volume || 0;

    let phase: 'accumulation' | 'markup' | 'distribution' | 'markdown' =
      'accumulation';

    if (currentVolume > avgVolume * 1.5) {
      if (indicators.rsi > 70) {
        phase = 'distribution';
      } else if (indicators.rsi < 30) {
        phase = 'accumulation';
      } else {
        phase =
          indicators.ema > candles[candles.length - 1].close
            ? 'markdown'
            : 'markup';
      }
    }

    return {
      phase,
      position: 'phase-' + phase.charAt(0)
    };
  }

  /**
   * Calculate overall confidence from all analysis methods
   */
  private calculateOverallConfidence(
    analysis: SignalAnalysis,
    indicatorAnalysis: any,
    patternAnalysis: any
  ): number {
    let totalConfidence = 0;
    let factors = 0;

    // Indicator confidence (40% weight)
    if (indicatorAnalysis?.confluence?.confidence) {
      totalConfidence += indicatorAnalysis.confluence.confidence * 0.4;
      factors += 0.4;
    }

    // Pattern confidence (30% weight)
    if (patternAnalysis?.confluence?.confidence) {
      totalConfidence += patternAnalysis.confluence.confidence * 0.3;
      factors += 0.3;
    }

    // Price action confidence (15% weight)
    if (analysis.priceAction.strength > 0) {
      totalConfidence += analysis.priceAction.strength * 0.15;
      factors += 0.15;
    }

    // ICT/SMC/Elliott/Wyckoff confluence (15% weight total)
    let advancedScore = 0;
    if (analysis.ict.orderBlock) advancedScore += 25;
    if (analysis.ict.fairValueGap) advancedScore += 25;
    if (analysis.smc.changeOfCharacter) advancedScore += 25;
    if (analysis.smc.breakOfStructure) advancedScore += 25;

    totalConfidence += (advancedScore / 100) * 100 * 0.15;
    factors += 0.15;

    return factors > 0 ? totalConfidence / factors : 0;
  }

  /**
   * Determine signal direction from analysis
   */
  private determineSignalDirection(
    analysis: SignalAnalysis,
    indicatorAnalysis: any,
    patternAnalysis: any
  ): 'BUY' | 'SELL' | null {
    let bullishSignals = 0;
    let bearishSignals = 0;

    // Indicator signals
    if (indicatorAnalysis?.confluence?.overall_bias === 'bullish')
      bullishSignals++;
    if (indicatorAnalysis?.confluence?.overall_bias === 'bearish')
      bearishSignals++;

    // Pattern signals
    if (patternAnalysis?.confluence?.overall_sentiment === 'bullish')
      bullishSignals++;
    if (patternAnalysis?.confluence?.overall_sentiment === 'bearish')
      bearishSignals++;

    // Price action signals
    if (analysis.priceAction.trend === 'bullish') bullishSignals++;
    if (analysis.priceAction.trend === 'bearish') bearishSignals++;

    // SMC signals
    if (analysis.smc.orderFlow === 'bullish') bullishSignals++;
    if (analysis.smc.orderFlow === 'bearish') bearishSignals++;

    // Require clear directional bias (lowered threshold for testing)
    if (bullishSignals > bearishSignals && bullishSignals >= 1) return 'BUY';
    if (bearishSignals > bullishSignals && bearishSignals >= 1) return 'SELL';

    return null;
  }

  /**
   * Calculate trading levels (entry, stop loss, take profit)
   */
  private calculateTradingLevels(
    currentPrice: number,
    signalType: 'BUY' | 'SELL',
    analysis: SignalAnalysis
  ): {
    entry: number;
    stopLoss: number;
    takeProfit: number;
    riskReward: number;
  } {
    const pipValue = 0.0001; // For EUR/USD
    const stopLossPips = this.signalConfig.stopLossPips;
    const takeProfitPips = this.signalConfig.takeProfitPips;

    let entry = currentPrice;
    let stopLoss: number;
    let takeProfit: number;

    if (signalType === 'BUY') {
      stopLoss = entry - stopLossPips * pipValue;
      takeProfit = entry + takeProfitPips * pipValue;

      // Adjust based on support/resistance
      if (
        analysis.priceAction.support > 0 &&
        analysis.priceAction.support < stopLoss
      ) {
        stopLoss = analysis.priceAction.support - 5 * pipValue; // 5 pips below support
      }
    } else {
      stopLoss = entry + stopLossPips * pipValue;
      takeProfit = entry - takeProfitPips * pipValue;

      // Adjust based on support/resistance
      if (
        analysis.priceAction.resistance > 0 &&
        analysis.priceAction.resistance > stopLoss
      ) {
        stopLoss = analysis.priceAction.resistance + 5 * pipValue; // 5 pips above resistance
      }
    }

    const riskReward =
      Math.abs(takeProfit - entry) / Math.abs(entry - stopLoss);

    return {
      entry,
      stopLoss,
      takeProfit,
      riskReward
    };
  }

  /**
   * Generate human-readable reasoning for the signal
   */
  private generateReasoning(
    analysis: SignalAnalysis,
    indicatorAnalysis: any,
    patternAnalysis: any
  ): string {
    const reasons: string[] = [];

    // Indicator reasoning
    if (indicatorAnalysis?.confluence?.overall_bias !== 'neutral') {
      reasons.push(
        `Technical indicators show ${
          indicatorAnalysis.confluence.overall_bias
        } bias (${indicatorAnalysis.confluence.confidence.toFixed(
          0
        )}% confidence)`
      );
    }

    // Pattern reasoning
    if (patternAnalysis?.patterns?.length > 0) {
      const strongPatterns = patternAnalysis.patterns.filter(
        (p: any) => p.strength > 80
      );
      if (strongPatterns.length > 0) {
        reasons.push(
          `Strong candlestick patterns detected: ${strongPatterns
            .map((p: any) => p.name)
            .join(', ')}`
        );
      }
    }

    // Price action reasoning
    if (analysis.priceAction.trend !== 'sideways') {
      reasons.push(
        `Price action shows ${
          analysis.priceAction.trend
        } trend (strength: ${analysis.priceAction.strength.toFixed(0)})`
      );
    }

    // ICT reasoning
    if (analysis.ict.orderBlock || analysis.ict.fairValueGap) {
      const ictFactors = [];
      if (analysis.ict.orderBlock) ictFactors.push('Order Block');
      if (analysis.ict.fairValueGap) ictFactors.push('Fair Value Gap');
      reasons.push(`ICT concepts: ${ictFactors.join(', ')}`);
    }

    // SMC reasoning
    if (analysis.smc.changeOfCharacter || analysis.smc.breakOfStructure) {
      const smcFactors = [];
      if (analysis.smc.changeOfCharacter)
        smcFactors.push('Change of Character');
      if (analysis.smc.breakOfStructure) smcFactors.push('Break of Structure');
      reasons.push(`Smart Money Concepts: ${smcFactors.join(', ')}`);
    }

    return reasons.length > 0
      ? reasons.join('. ')
      : 'Multiple confluence factors align for this signal.';
  }

  /**
   * Check if we can generate a signal for this symbol
   */
  private canGenerateSignal(symbol: string, timestamp: number): boolean {
    // Check daily limit
    const today = new Date(timestamp).toDateString();
    const dailyKey = `${symbol}-${today}`;
    const dailyCount = this.dailySignalCount.get(dailyKey) || 0;

    if (dailyCount >= this.signalConfig.maxSignalsPerDay) {
      return false;
    }

    // Check minimum interval between signals
    const lastSignal = this.lastSignalTime.get(symbol) || 0;
    if (timestamp - lastSignal < this.minSignalInterval) {
      return false;
    }

    return true;
  }

  /**
   * Store signal in history
   */
  private storeSignal(symbol: string, signal: AdvancedSignal): void {
    if (!this.signalHistory.has(symbol)) {
      this.signalHistory.set(symbol, []);
    }

    const history = this.signalHistory.get(symbol)!;
    history.push(signal);

    // Maintain max history length
    if (history.length > this.maxHistoryLength) {
      history.shift();
    }
  }

  /**
   * Update signal counters
   */
  private updateSignalCounters(symbol: string, timestamp: number): void {
    const today = new Date(timestamp).toDateString();
    const dailyKey = `${symbol}-${today}`;

    this.dailySignalCount.set(
      dailyKey,
      (this.dailySignalCount.get(dailyKey) || 0) + 1
    );
    this.lastSignalTime.set(symbol, timestamp);
  }

  /**
   * Reset daily counters (should be called daily)
   */
  private resetDailyCounters(): void {
    this.dailySignalCount.clear();

    // Set up daily reset
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
      this.resetDailyCounters();
    }, msUntilMidnight);
  }

  /**
   * Get signal history for a symbol
   */
  public getSignalHistory(symbol: string, limit?: number): AdvancedSignal[] {
    const history = this.signalHistory.get(symbol) || [];
    return limit ? history.slice(-limit) : [...history];
  }

  /**
   * Get latest signal for a symbol
   */
  public getLatestSignal(symbol: string): AdvancedSignal | null {
    const history = this.signalHistory.get(symbol);
    return history && history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * Update signal configuration
   */
  public updateConfig(newConfig: Partial<SignalConfig>): void {
    this.signalConfig = { ...this.signalConfig, ...newConfig };
    logger.trading('info', 'Signal configuration updated', {
      config: this.signalConfig
    });
  }

  /**
   * Get current configuration
   */
  public getConfig(): SignalConfig {
    return { ...this.signalConfig };
  }

  /**
   * Get signal statistics
   */
  public getSignalStatistics(
    symbol: string,
    timeframe: number = 86400000
  ): any {
    const history = this.signalHistory.get(symbol) || [];
    const cutoffTime = Date.now() - timeframe;
    const recentSignals = history.filter((s) => s.timestamp > cutoffTime);

    return {
      total_signals: recentSignals.length,
      buy_signals: recentSignals.filter((s) => s.type === 'BUY').length,
      sell_signals: recentSignals.filter((s) => s.type === 'SELL').length,
      average_confidence:
        recentSignals.reduce((sum, s) => sum + s.confidence, 0) /
          recentSignals.length || 0,
      high_confidence_signals: recentSignals.filter((s) => s.confidence > 80)
        .length
    };
  }

  /**
   * Clear signal history for a symbol
   */
  public clearHistory(symbol: string): void {
    this.signalHistory.delete(symbol);
    logger.trading('info', `Cleared signal history for ${symbol}`);
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.signalHistory.keys());
  }
}

// Export singleton instance
export const advancedSignalEngine = new AdvancedSignalEngine();
