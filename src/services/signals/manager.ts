import { EventEmitter } from 'events';
import { AdvancedSignal } from '../../types';
import { logger } from '../../utils/logger';
import { advancedSignalEngine, SignalGenerationInput } from './index';

export interface SignalManagerConfig {
  enabled: boolean;
  autoTrading: boolean;
  riskManagement: {
    maxConcurrentSignals: number;
    maxDailyRisk: number;
    positionSizing: 'fixed' | 'percentage' | 'kelly';
  };
  filters: {
    minConfidence: number;
    maxSpread: number;
    tradingHours: {
      start: string;
      end: string;
      timezone: string;
    };
  };
}

export class SignalManager extends EventEmitter {
  private config: SignalManagerConfig;
  private activeSignals: Map<string, AdvancedSignal[]> = new Map();
  private signalQueue: AdvancedSignal[] = [];
  private isProcessing = false;

  constructor() {
    super();
    this.config = this.initializeConfig();
    this.setupEventHandlers();
  }

  private initializeConfig(): SignalManagerConfig {
    return {
      enabled: true,
      autoTrading: false, // Manual approval required by default
      riskManagement: {
        maxConcurrentSignals: 3,
        maxDailyRisk: 5, // 5% of account
        positionSizing: 'percentage'
      },
      filters: {
        minConfidence: 30, // Lowered for testing
        maxSpread: 3, // 3 pips
        tradingHours: {
          start: '00:00', // Allow 24/7 trading for testing
          end: '23:59',
          timezone: 'UTC'
        }
      }
    };
  }

  private setupEventHandlers(): void {
    // Listen to signal generation events
    advancedSignalEngine.on('signalGenerated', (signal: AdvancedSignal) => {
      this.handleNewSignal(signal);
    });

    // Listen to signal ready events for Telegram notifications
    this.on('signalReady', async (data) => {
      await this.sendSignalToTelegram(data);
    });
  }

  /**
   * Process market analysis and generate signals
   */
  public async processMarketAnalysis(data: {
    symbol: string;
    indicatorAnalysis: any;
    patternAnalysis: any;
    indicatorResult: any;
    patternResult: any;
    marketData: any;
  }): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const {
        symbol,
        indicatorAnalysis,
        patternAnalysis,
        indicatorResult,
        patternResult,
        marketData
      } = data;

      // Prepare signal generation input
      const input: SignalGenerationInput = {
        symbol,
        timestamp: Date.now(),
        price: marketData.candles[marketData.candles.length - 1]?.close || 0,
        candles: marketData.candles,
        indicators: indicatorResult.indicators,
        patterns: patternResult.patterns,
        indicatorAnalysis,
        patternAnalysis
      };

      // Generate signal
      const signal = await advancedSignalEngine.generateSignal(input);

      if (signal) {
        logger.trading('info', `Signal generated by manager for ${symbol}`, {
          type: signal.type,
          confidence: signal.confidence
        });

        logger.trading('info', 'About to call handleNewSignal');

        // Process the signal
        await this.handleNewSignal(signal);

        logger.trading('info', 'handleNewSignal completed');
      }
    } catch (error) {
      logger.trading(
        'error',
        'Failed to process market analysis for signal generation',
        { error }
      );
    }
  }

  /**
   * Handle new signal generation
   */
  private async handleNewSignal(signal: AdvancedSignal): Promise<void> {
    logger.trading('info', 'handleNewSignal called', {
      symbol: signal.symbol,
      type: signal.type,
      confidence: signal.confidence
    });

    try {
      // Apply filters
      if (!this.passesFilters(signal)) {
        logger.trading('debug', `Signal filtered out for ${signal.symbol}`, {
          confidence: signal.confidence,
          minRequired: this.config.filters.minConfidence
        });
        return;
      }

      // Check risk management
      if (!this.passesRiskManagement(signal)) {
        logger.trading(
          'warn',
          `Signal rejected due to risk management for ${signal.symbol}`,
          {
            activeSignals: this.getActiveSignalCount(signal.symbol),
            maxAllowed: this.config.riskManagement.maxConcurrentSignals
          }
        );
        return;
      }

      // Add to queue for processing
      this.signalQueue.push(signal);

      // Process queue
      await this.processSignalQueue();
    } catch (error) {
      logger.trading(
        'error',
        `Failed to handle new signal for ${signal.symbol}`,
        { error }
      );
    }
  }

  /**
   * Apply signal filters
   */
  private passesFilters(signal: AdvancedSignal): boolean {
    // Confidence filter
    if (signal.confidence < this.config.filters.minConfidence) {
      return false;
    }

    // Trading hours filter
    if (!this.isWithinTradingHours()) {
      return false;
    }

    // Spread filter (would need real-time spread data)
    // For now, we'll assume spreads are acceptable

    return true;
  }

  /**
   * Check risk management rules
   */
  private passesRiskManagement(signal: AdvancedSignal): boolean {
    // Check concurrent signals limit
    const activeCount = this.getActiveSignalCount(signal.symbol);
    if (activeCount >= this.config.riskManagement.maxConcurrentSignals) {
      return false;
    }

    // Check daily risk limit (simplified)
    const dailyRisk = this.calculateDailyRisk();
    if (dailyRisk >= this.config.riskManagement.maxDailyRisk) {
      return false;
    }

    return true;
  }

  /**
   * Check if current time is within trading hours
   */
  private isWithinTradingHours(): boolean {
    const now = new Date();
    const currentHour = now.getUTCHours();
    const currentMinute = now.getUTCMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    const [startHour, startMinute] = this.config.filters.tradingHours.start
      .split(':')
      .map(Number);
    const [endHour, endMinute] = this.config.filters.tradingHours.end
      .split(':')
      .map(Number);

    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;

    return currentTime >= startTime && currentTime <= endTime;
  }

  /**
   * Process signal queue
   */
  private async processSignalQueue(): Promise<void> {
    if (this.isProcessing || this.signalQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.signalQueue.length > 0) {
        const signal = this.signalQueue.shift()!;
        await this.processSignal(signal);
      }
    } catch (error) {
      logger.trading('error', 'Error processing signal queue', { error });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process individual signal
   */
  private async processSignal(signal: AdvancedSignal): Promise<void> {
    try {
      // Add to active signals
      this.addActiveSignal(signal);

      // Calculate position size
      const positionSize = this.calculatePositionSize(signal);

      // Emit signal for external processing (Telegram, database, etc.)
      this.emit('signalReady', {
        signal,
        positionSize,
        timestamp: Date.now()
      });

      // Process signal for notification (signal-only mode)
      await this.processSignalForNotification(signal, positionSize);
    } catch (error) {
      logger.trading('error', `Failed to process signal for ${signal.symbol}`, {
        error
      });
    }
  }

  /**
   * Calculate position size based on risk management
   */
  private calculatePositionSize(signal: AdvancedSignal): number {
    const accountBalance = 10000; // This should come from account info
    const riskPerTrade = 0.02; // 2% risk per trade

    const riskAmount = accountBalance * riskPerTrade;
    const stopLossDistance = Math.abs(signal.entryPrice - signal.stopLoss);

    // Calculate position size based on risk
    const positionSize = riskAmount / stopLossDistance;

    // Apply position sizing method
    switch (this.config.riskManagement.positionSizing) {
      case 'fixed':
        return 0.1; // Fixed lot size
      case 'percentage':
        return Math.min(positionSize, 1.0); // Max 1 lot
      case 'kelly':
        // Simplified Kelly criterion
        const winRate = 0.6; // This should be calculated from historical data
        const avgWin = signal.riskRewardRatio;
        const avgLoss = 1;
        const kellyPercent =
          (winRate * avgWin - (1 - winRate) * avgLoss) / avgWin;
        return Math.max(
          0.01,
          Math.min((kellyPercent * accountBalance) / 10000, 1.0)
        );
      default:
        return 0.1;
    }
  }

  /**
   * Process signal for notification (signal-only mode)
   */
  private async processSignalForNotification(
    signal: AdvancedSignal,
    positionSize: number
  ): Promise<void> {
    try {
      logger.trading(
        'info',
        `Processing signal for notification: ${signal.symbol} ${signal.type}`,
        {
          entry: signal.entryPrice,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit,
          positionSize,
          confidence: signal.confidence
        }
      );

      // Update signal status to ready for manual execution
      signal.status = 'pending';

      // Emit signal ready event
      this.emit('signalReady', {
        signal,
        positionSize,
        timestamp: Date.now(),
        message: `Signal ready for manual execution: ${signal.symbol} ${signal.type}`
      });

      // Send to Telegram
      logger.trading('info', 'Attempting to send signal to Telegram', {
        symbol: signal.symbol,
        type: signal.type,
        confidence: signal.confidence
      });

      const { telegramService } = await import('../telegram');
      await telegramService.sendSignal({
        signal,
        positionSize,
        timestamp: Date.now()
      });

      logger.trading('info', 'Signal sent to Telegram successfully');

      logger.trading('info', `Signal notification sent: ${signal.symbol}`, {
        type: signal.type,
        confidence: signal.confidence,
        positionSize
      });
    } catch (error) {
      logger.trading(
        'error',
        `Failed to process signal notification: ${signal.symbol}`,
        {
          error
        }
      );
      signal.status = 'cancelled';

      // Emit notification error event
      this.emit('signalNotificationError', {
        signal,
        positionSize,
        error,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Add signal to active signals
   */
  private addActiveSignal(signal: AdvancedSignal): void {
    if (!this.activeSignals.has(signal.symbol)) {
      this.activeSignals.set(signal.symbol, []);
    }
    this.activeSignals.get(signal.symbol)!.push(signal);
  }

  /**
   * Get active signal count for a symbol
   */
  private getActiveSignalCount(symbol: string): number {
    const signals = this.activeSignals.get(symbol) || [];
    return signals.filter(
      (s) => s.status === 'pending' || s.status === 'filled'
    ).length;
  }

  /**
   * Calculate current daily risk exposure
   */
  private calculateDailyRisk(): number {
    // Simplified calculation - in reality, this would be more complex
    const today = new Date().toDateString();
    let totalRisk = 0;

    for (const signals of this.activeSignals.values()) {
      const todaySignals = signals.filter(
        (s) =>
          new Date(s.timestamp).toDateString() === today &&
          (s.status === 'pending' || s.status === 'filled')
      );
      totalRisk += todaySignals.length * 2; // Assume 2% risk per signal
    }

    return totalRisk;
  }

  /**
   * Close signal (when TP/SL is hit or manual close)
   */
  public closeSignal(
    signalId: string,
    reason: 'take_profit' | 'stop_loss' | 'manual'
  ): void {
    for (const [symbol, signals] of this.activeSignals.entries()) {
      const signalIndex = signals.findIndex((s) => s.id === signalId);
      if (signalIndex !== -1) {
        const signal = signals[signalIndex];
        signal.status = 'filled'; // Or create a 'closed' status

        this.emit('signalClosed', {
          signal,
          reason,
          closeTime: Date.now()
        });

        logger.trading(
          'info',
          `Signal closed: ${signal.symbol} ${signal.type}`,
          {
            reason,
            entry: signal.entryPrice,
            confidence: signal.confidence
          }
        );

        // Remove from active signals
        signals.splice(signalIndex, 1);
        break;
      }
    }
  }

  /**
   * Get active signals for a symbol
   */
  public getActiveSignals(symbol?: string): AdvancedSignal[] {
    if (symbol) {
      return this.activeSignals.get(symbol) || [];
    }

    const allSignals: AdvancedSignal[] = [];
    for (const signals of this.activeSignals.values()) {
      allSignals.push(...signals);
    }
    return allSignals;
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<SignalManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.trading('info', 'Signal manager configuration updated', {
      config: this.config
    });
  }

  /**
   * Get current configuration
   */
  public getConfig(): SignalManagerConfig {
    return { ...this.config };
  }

  /**
   * Get signal statistics
   */
  public getStatistics(): any {
    const allSignals: AdvancedSignal[] = [];
    for (const signals of this.activeSignals.values()) {
      allSignals.push(...signals);
    }

    const today = new Date().toDateString();
    const todaySignals = allSignals.filter(
      (s) => new Date(s.timestamp).toDateString() === today
    );

    return {
      total_active_signals: allSignals.length,
      today_signals: todaySignals.length,
      buy_signals: allSignals.filter((s) => s.type === 'BUY').length,
      sell_signals: allSignals.filter((s) => s.type === 'SELL').length,
      average_confidence:
        allSignals.reduce((sum, s) => sum + s.confidence, 0) /
          allSignals.length || 0,
      high_confidence_signals: allSignals.filter((s) => s.confidence > 85)
        .length,
      queue_length: this.signalQueue.length
    };
  }

  /**
   * Enable/disable signal generation
   */
  public setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    logger.trading(
      'info',
      `Signal manager ${enabled ? 'enabled' : 'disabled'}`
    );
  }

  /**
   * Enable/disable auto-trading
   */
  public setAutoTrading(enabled: boolean): void {
    this.config.autoTrading = enabled;
    logger.trading('info', `Auto-trading ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Send signal to Telegram
   */
  private async sendSignalToTelegram(data: any): Promise<void> {
    try {
      // Import Telegram service dynamically
      const { telegramService } = await import('../telegram');

      if (telegramService.isReady()) {
        await telegramService.sendSignal(data);
        logger.trading(
          'info',
          `Signal sent to Telegram for ${data.signal.symbol}`
        );
      } else {
        logger.trading('warn', 'Telegram service not ready, signal not sent');
      }
    } catch (error) {
      logger.trading('error', 'Failed to send signal to Telegram', { error });
    }
  }
}

// Export singleton instance
export const signalManager = new SignalManager();
