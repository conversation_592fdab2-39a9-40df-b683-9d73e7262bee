import { EventEmitter } from 'events';
import { CandlestickData, TechnicalIndicators } from '../../types';
import { logger } from '../../utils/logger';
import { IndicatorResult } from './index';

export interface IndicatorAnalysis {
  symbol: string;
  timestamp: number;
  signals: {
    rsi: {
      value: number;
      signal: 'overbought' | 'oversold' | 'neutral';
      strength: number;
      divergence?: 'bullish' | 'bearish' | null;
    };
    macd: {
      macd: number;
      signal: number;
      histogram: number;
      crossover: 'bullish' | 'bearish' | 'neutral';
      trend: 'bullish' | 'bearish' | 'neutral';
    };
    ema: {
      value: number;
      trend: 'bullish' | 'bearish' | 'sideways';
      support_resistance: 'support' | 'resistance' | 'neutral';
    };
    bollingerBands: {
      upper: number;
      middle: number;
      lower: number;
      position: 'above_upper' | 'below_lower' | 'middle' | 'neutral';
      squeeze: boolean;
      expansion: boolean;
    };
  };
  confluence: {
    bullish_signals: number;
    bearish_signals: number;
    neutral_signals: number;
    overall_bias: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
  };
  momentum: {
    strength: 'strong' | 'moderate' | 'weak';
    direction: 'bullish' | 'bearish' | 'neutral';
    acceleration: 'increasing' | 'decreasing' | 'stable';
  };
}

export class IndicatorAnalyzer extends EventEmitter {
  private analysisHistory: Map<string, IndicatorAnalysis[]> = new Map();
  private readonly maxHistoryLength = 100;

  constructor() {
    super();
  }

  /**
   * Analyze technical indicators and generate comprehensive analysis
   */
  public analyzeIndicators(
    symbol: string,
    currentData: IndicatorResult,
    priceData: CandlestickData[],
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis {
    try {
      const analysis: IndicatorAnalysis = {
        symbol,
        timestamp: currentData.timestamp,
        signals: this.analyzeSignals(currentData, priceData, previousAnalysis),
        confluence: {
          bullish_signals: 0,
          bearish_signals: 0,
          neutral_signals: 0,
          overall_bias: 'neutral',
          confidence: 0
        },
        momentum: {
          strength: 'weak',
          direction: 'neutral',
          acceleration: 'stable'
        }
      };

      // Calculate confluence
      analysis.confluence = this.calculateConfluence(analysis.signals);

      // Analyze momentum
      analysis.momentum = this.analyzeMomentum(
        currentData,
        priceData,
        previousAnalysis
      );

      // Store analysis in history
      this.storeAnalysis(symbol, analysis);

      // Emit analysis event
      this.emit('analysisComplete', analysis);

      logger.indicators('debug', `Analysis complete for ${symbol}`, {
        bias: analysis.confluence.overall_bias,
        confidence: analysis.confluence.confidence,
        momentum: analysis.momentum.direction
      });

      return analysis;
    } catch (error) {
      logger.indicators('error', `Failed to analyze indicators for ${symbol}`, {
        error
      });
      throw error;
    }
  }

  /**
   * Analyze individual indicator signals
   */
  private analyzeSignals(
    currentData: IndicatorResult,
    priceData: CandlestickData[],
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis['signals'] {
    const { indicators } = currentData;
    const currentPrice = priceData[priceData.length - 1]?.close || 0;

    return {
      rsi: this.analyzeRSI(indicators.rsi, priceData, previousAnalysis),
      macd: this.analyzeMacd(indicators.macd, previousAnalysis),
      ema: this.analyzeEMA(indicators.ema, currentPrice, priceData),
      bollingerBands: this.analyzeBollingerBands(
        indicators.bollingerBands,
        currentPrice,
        previousAnalysis
      )
    };
  }

  /**
   * Analyze RSI signals including divergence detection
   */
  private analyzeRSI(
    rsi: number,
    priceData: CandlestickData[],
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis['signals']['rsi'] {
    let signal: 'overbought' | 'oversold' | 'neutral' = 'neutral';
    let strength = 0;

    // Determine RSI signal
    if (rsi >= 70) {
      signal = 'overbought';
      strength = Math.min(100, (rsi - 70) * 3.33); // Scale 70-100 to 0-100
    } else if (rsi <= 30) {
      signal = 'oversold';
      strength = Math.min(100, (30 - rsi) * 3.33); // Scale 30-0 to 0-100
    } else {
      strength = Math.abs(50 - rsi) * 2; // Distance from neutral (50)
    }

    // Detect divergence (simplified)
    let divergence: 'bullish' | 'bearish' | null = null;
    if (priceData.length >= 20 && previousAnalysis) {
      divergence = this.detectRSIDivergence(
        rsi,
        priceData,
        previousAnalysis.signals.rsi.value
      );
    }

    return {
      value: rsi,
      signal,
      strength,
      divergence
    };
  }

  /**
   * Analyze MACD signals
   */
  private analyzeMacd(
    macd: TechnicalIndicators['macd'],
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis['signals']['macd'] {
    let crossover: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    let trend: 'bullish' | 'bearish' | 'neutral' = 'neutral';

    // Determine crossover
    if (previousAnalysis) {
      const prevMacd = previousAnalysis.signals.macd;
      const currentAbove = macd.macd > macd.signal;
      const previousAbove = prevMacd.macd > prevMacd.signal;

      if (currentAbove && !previousAbove) {
        crossover = 'bullish';
      } else if (!currentAbove && previousAbove) {
        crossover = 'bearish';
      }
    }

    // Determine trend
    if (macd.macd > macd.signal && macd.histogram > 0) {
      trend = 'bullish';
    } else if (macd.macd < macd.signal && macd.histogram < 0) {
      trend = 'bearish';
    }

    return {
      macd: macd.macd,
      signal: macd.signal,
      histogram: macd.histogram,
      crossover,
      trend
    };
  }

  /**
   * Analyze EMA signals
   */
  private analyzeEMA(
    ema: number,
    currentPrice: number,
    priceData: CandlestickData[]
  ): IndicatorAnalysis['signals']['ema'] {
    let trend: 'bullish' | 'bearish' | 'sideways' = 'sideways';
    let support_resistance: 'support' | 'resistance' | 'neutral' = 'neutral';

    // Determine trend
    const priceDiff = ((currentPrice - ema) / ema) * 100;
    if (priceDiff > 0.1) {
      trend = 'bullish';
    } else if (priceDiff < -0.1) {
      trend = 'bearish';
    }

    // Determine support/resistance
    if (priceData.length >= 5) {
      const recentCandles = priceData.slice(-5);
      const touchesEMA = recentCandles.some(
        (candle) =>
          Math.abs(candle.low - ema) / ema < 0.001 ||
          Math.abs(candle.high - ema) / ema < 0.001
      );

      if (touchesEMA) {
        support_resistance = currentPrice > ema ? 'support' : 'resistance';
      }
    }

    return {
      value: ema,
      trend,
      support_resistance
    };
  }

  /**
   * Analyze Bollinger Bands signals
   */
  private analyzeBollingerBands(
    bb: TechnicalIndicators['bollingerBands'],
    currentPrice: number,
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis['signals']['bollingerBands'] {
    let position: 'above_upper' | 'below_lower' | 'middle' | 'neutral' =
      'neutral';
    let squeeze = false;
    let expansion = false;

    // Determine position
    if (currentPrice > bb.upper) {
      position = 'above_upper';
    } else if (currentPrice < bb.lower) {
      position = 'below_lower';
    } else if (Math.abs(currentPrice - bb.middle) / bb.middle < 0.005) {
      position = 'middle';
    }

    // Detect squeeze and expansion
    const bandWidth = (bb.upper - bb.lower) / bb.middle;
    squeeze = bandWidth < 0.02; // 2% band width indicates squeeze

    if (previousAnalysis) {
      const prevBandWidth =
        (previousAnalysis.signals.bollingerBands.upper -
          previousAnalysis.signals.bollingerBands.lower) /
        previousAnalysis.signals.bollingerBands.middle;

      expansion = bandWidth > prevBandWidth * 1.1; // 10% increase in band width
    }

    return {
      upper: bb.upper,
      middle: bb.middle,
      lower: bb.lower,
      position,
      squeeze,
      expansion
    };
  }

  /**
   * Calculate confluence of signals
   */
  private calculateConfluence(
    signals: IndicatorAnalysis['signals']
  ): IndicatorAnalysis['confluence'] {
    let bullish_signals = 0;
    let bearish_signals = 0;
    let neutral_signals = 0;

    // RSI signals
    if (signals.rsi.signal === 'oversold') bullish_signals++;
    else if (signals.rsi.signal === 'overbought') bearish_signals++;
    else neutral_signals++;

    // MACD signals
    if (
      signals.macd.trend === 'bullish' ||
      signals.macd.crossover === 'bullish'
    )
      bullish_signals++;
    else if (
      signals.macd.trend === 'bearish' ||
      signals.macd.crossover === 'bearish'
    )
      bearish_signals++;
    else neutral_signals++;

    // EMA signals
    if (signals.ema.trend === 'bullish') bullish_signals++;
    else if (signals.ema.trend === 'bearish') bearish_signals++;
    else neutral_signals++;

    // Bollinger Bands signals
    if (signals.bollingerBands.position === 'below_lower') bullish_signals++;
    else if (signals.bollingerBands.position === 'above_upper')
      bearish_signals++;
    else neutral_signals++;

    // Determine overall bias
    let overall_bias: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (bullish_signals > bearish_signals) {
      overall_bias = 'bullish';
    } else if (bearish_signals > bullish_signals) {
      overall_bias = 'bearish';
    }

    // Calculate confidence
    const totalSignals = bullish_signals + bearish_signals + neutral_signals;
    const dominantSignals = Math.max(bullish_signals, bearish_signals);
    const confidence =
      totalSignals > 0 ? (dominantSignals / totalSignals) * 100 : 0;

    return {
      bullish_signals,
      bearish_signals,
      neutral_signals,
      overall_bias,
      confidence
    };
  }

  /**
   * Analyze momentum
   */
  private analyzeMomentum(
    currentData: IndicatorResult,
    priceData: CandlestickData[],
    previousAnalysis?: IndicatorAnalysis
  ): IndicatorAnalysis['momentum'] {
    let strength: 'strong' | 'moderate' | 'weak' = 'weak';
    let direction: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    let acceleration: 'increasing' | 'decreasing' | 'stable' = 'stable';

    // Analyze strength based on RSI and MACD histogram
    const rsi = currentData.indicators.rsi;
    const histogram = Math.abs(currentData.indicators.macd.histogram);

    if ((rsi > 60 || rsi < 40) && histogram > 0.001) {
      strength = 'strong';
    } else if ((rsi > 55 || rsi < 45) && histogram > 0.0005) {
      strength = 'moderate';
    }

    // Determine direction
    if (rsi > 50 && currentData.indicators.macd.histogram > 0) {
      direction = 'bullish';
    } else if (rsi < 50 && currentData.indicators.macd.histogram < 0) {
      direction = 'bearish';
    }

    // Analyze acceleration
    if (previousAnalysis && priceData.length >= 3) {
      const currentMomentum = Math.abs(currentData.indicators.macd.histogram);
      const previousMomentum = Math.abs(
        previousAnalysis.signals.macd.histogram
      );

      if (currentMomentum > previousMomentum * 1.1) {
        acceleration = 'increasing';
      } else if (currentMomentum < previousMomentum * 0.9) {
        acceleration = 'decreasing';
      }
    }

    return {
      strength,
      direction,
      acceleration
    };
  }

  /**
   * Detect RSI divergence (simplified implementation)
   */
  private detectRSIDivergence(
    currentRSI: number,
    priceData: CandlestickData[],
    previousRSI: number
  ): 'bullish' | 'bearish' | null {
    if (priceData.length < 2) return null;

    const currentPrice = priceData[priceData.length - 1].close;
    const previousPrice = priceData[priceData.length - 2].close;

    // Bullish divergence: price makes lower low, RSI makes higher low
    if (
      currentPrice < previousPrice &&
      currentRSI > previousRSI &&
      currentRSI < 40
    ) {
      return 'bullish';
    }

    // Bearish divergence: price makes higher high, RSI makes lower high
    if (
      currentPrice > previousPrice &&
      currentRSI < previousRSI &&
      currentRSI > 60
    ) {
      return 'bearish';
    }

    return null;
  }

  /**
   * Store analysis in history
   */
  private storeAnalysis(symbol: string, analysis: IndicatorAnalysis): void {
    if (!this.analysisHistory.has(symbol)) {
      this.analysisHistory.set(symbol, []);
    }

    const history = this.analysisHistory.get(symbol)!;
    history.push(analysis);

    // Maintain max history length
    if (history.length > this.maxHistoryLength) {
      history.shift();
    }
  }

  /**
   * Get analysis history for a symbol
   */
  public getAnalysisHistory(
    symbol: string,
    limit?: number
  ): IndicatorAnalysis[] {
    const history = this.analysisHistory.get(symbol) || [];
    return limit ? history.slice(-limit) : [...history];
  }

  /**
   * Get latest analysis for a symbol
   */
  public getLatestAnalysis(symbol: string): IndicatorAnalysis | null {
    const history = this.analysisHistory.get(symbol);
    return history && history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * Clear analysis history for a symbol
   */
  public clearHistory(symbol: string): void {
    this.analysisHistory.delete(symbol);
    logger.indicators('info', `Cleared analysis history for ${symbol}`);
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.analysisHistory.keys());
  }
}

// Export singleton instance
export const indicatorAnalyzer = new IndicatorAnalyzer();
