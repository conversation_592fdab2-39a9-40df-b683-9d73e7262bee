import { EventEmitter } from 'events';
import {
  ADX,
  ATR,
  BollingerBands,
  EMA,
  MACD,
  RSI,
  SMA,
  Stochastic
} from 'technicalindicators';
import { config } from '../../config';
import { CandlestickData, TechnicalIndicators } from '../../types';
import { logger } from '../../utils/logger';

export interface IndicatorResult {
  symbol: string;
  timestamp: number;
  indicators: TechnicalIndicators;
  additionalIndicators?: {
    sma?: number;
    stochastic?: { k: number; d: number };
    atr?: number;
    adx?: number;
  };
}

export interface IndicatorConfig {
  rsi: {
    period: number;
    overbought: number;
    oversold: number;
  };
  ema: {
    period: number;
  };
  macd: {
    fastPeriod: number;
    slowPeriod: number;
    signalPeriod: number;
  };
  bollingerBands: {
    period: number;
    stdDev: number;
  };
  sma: {
    period: number;
  };
  stochastic: {
    kPeriod: number;
    dPeriod: number;
  };
  atr: {
    period: number;
  };
  adx: {
    period: number;
  };
}

export class TechnicalIndicatorsEngine extends EventEmitter {
  private indicatorConfig: IndicatorConfig;
  private dataBuffers: Map<string, CandlestickData[]> = new Map();
  private lastCalculation: Map<string, IndicatorResult> = new Map();

  constructor() {
    super();
    this.indicatorConfig = this.initializeConfig();
  }

  private initializeConfig(): IndicatorConfig {
    return {
      rsi: {
        period: config.indicators.rsiPeriod,
        overbought: 70,
        oversold: 30
      },
      ema: {
        period: config.indicators.emaPeriod
      },
      macd: {
        fastPeriod: config.indicators.macdFast,
        slowPeriod: config.indicators.macdSlow,
        signalPeriod: config.indicators.macdSignal
      },
      bollingerBands: {
        period: config.indicators.bollingerPeriod,
        stdDev: config.indicators.bollingerStdDev
      },
      sma: {
        period: 50 // Default SMA period
      },
      stochastic: {
        kPeriod: 14,
        dPeriod: 3
      },
      atr: {
        period: 14
      },
      adx: {
        period: 14
      }
    };
  }

  /**
   * Update data buffer for a symbol and calculate indicators
   */
  public updateData(
    symbol: string,
    candles: CandlestickData[]
  ): IndicatorResult | null {
    try {
      // Update data buffer
      this.dataBuffers.set(symbol, [...candles]);

      // Calculate indicators
      const result = this.calculateIndicators(symbol, candles);

      if (result) {
        this.lastCalculation.set(symbol, result);
        this.emit('indicatorsUpdated', result);

        logger.indicators('debug', `Indicators updated for ${symbol}`, {
          rsi: result.indicators.rsi,
          ema: result.indicators.ema,
          macd: result.indicators.macd
        });
      }

      return result;
    } catch (error) {
      logger.indicators('error', `Failed to update indicators for ${symbol}`, {
        error
      });
      return null;
    }
  }

  /**
   * Calculate all technical indicators for a symbol
   */
  private calculateIndicators(
    symbol: string,
    candles: CandlestickData[]
  ): IndicatorResult | null {
    if (candles.length === 0) {
      return null;
    }

    const closes = candles.map((c) => c.close);
    const highs = candles.map((c) => c.high);
    const lows = candles.map((c) => c.low);
    const volumes = candles.map((c) => c.volume || 0);

    // Ensure we have enough data for calculations
    const minDataPoints = Math.max(
      this.indicatorConfig.rsi.period,
      this.indicatorConfig.ema.period,
      this.indicatorConfig.macd.slowPeriod +
        this.indicatorConfig.macd.signalPeriod,
      this.indicatorConfig.bollingerBands.period
    );

    if (candles.length < minDataPoints) {
      logger.indicators(
        'warn',
        `Insufficient data for ${symbol}: ${candles.length} < ${minDataPoints}`
      );
      return null;
    }

    try {
      // Calculate RSI
      const rsiValues = RSI.calculate({
        values: closes,
        period: this.indicatorConfig.rsi.period
      });
      const currentRSI = rsiValues[rsiValues.length - 1] || 0;

      // Calculate EMA
      const emaValues = EMA.calculate({
        values: closes,
        period: this.indicatorConfig.ema.period
      });
      const currentEMA = emaValues[emaValues.length - 1] || 0;

      // Calculate MACD
      const macdValues = MACD.calculate({
        values: closes,
        fastPeriod: this.indicatorConfig.macd.fastPeriod,
        slowPeriod: this.indicatorConfig.macd.slowPeriod,
        signalPeriod: this.indicatorConfig.macd.signalPeriod,
        SimpleMAOscillator: false,
        SimpleMASignal: false
      });
      const currentMACD = macdValues[macdValues.length - 1] || {
        MACD: 0,
        signal: 0,
        histogram: 0
      };
      const macdData = {
        macd: currentMACD.MACD || 0,
        signal: currentMACD.signal || 0,
        histogram: currentMACD.histogram || 0
      };

      // Calculate Bollinger Bands
      const bbValues = BollingerBands.calculate({
        values: closes,
        period: this.indicatorConfig.bollingerBands.period,
        stdDev: this.indicatorConfig.bollingerBands.stdDev
      });
      const currentBB = bbValues[bbValues.length - 1] || {
        upper: 0,
        middle: 0,
        lower: 0
      };

      // Calculate additional indicators
      const smaValues = SMA.calculate({
        values: closes,
        period: this.indicatorConfig.sma.period
      });
      const currentSMA = smaValues[smaValues.length - 1] || 0;

      const stochasticValues = Stochastic.calculate({
        high: highs,
        low: lows,
        close: closes,
        period: this.indicatorConfig.stochastic.kPeriod,
        signalPeriod: this.indicatorConfig.stochastic.dPeriod
      });
      const currentStochastic = stochasticValues[
        stochasticValues.length - 1
      ] || { k: 0, d: 0 };

      const atrValues = ATR.calculate({
        high: highs,
        low: lows,
        close: closes,
        period: this.indicatorConfig.atr.period
      });
      const currentATR = atrValues[atrValues.length - 1] || 0;

      const adxValues = ADX.calculate({
        high: highs,
        low: lows,
        close: closes,
        period: this.indicatorConfig.adx.period
      });
      const currentADX = adxValues[adxValues.length - 1];
      const adxValue =
        typeof currentADX === 'object' ? currentADX.adx || 0 : currentADX || 0;

      const result: IndicatorResult = {
        symbol,
        timestamp: candles[candles.length - 1].timestamp,
        indicators: {
          rsi: currentRSI,
          ema: currentEMA,
          macd: macdData,
          bollingerBands: {
            upper: currentBB.upper,
            middle: currentBB.middle,
            lower: currentBB.lower
          }
        },
        additionalIndicators: {
          sma: currentSMA,
          stochastic: {
            k: currentStochastic.k,
            d: currentStochastic.d
          },
          atr: currentATR,
          adx: adxValue
        }
      };

      return result;
    } catch (error) {
      logger.indicators('error', `Error calculating indicators for ${symbol}`, {
        error
      });
      return null;
    }
  }

  /**
   * Get the latest indicator values for a symbol
   */
  public getLatestIndicators(symbol: string): IndicatorResult | null {
    return this.lastCalculation.get(symbol) || null;
  }

  /**
   * Get RSI signal (overbought/oversold)
   */
  public getRSISignal(rsi: number): 'overbought' | 'oversold' | 'neutral' {
    if (rsi >= this.indicatorConfig.rsi.overbought) {
      return 'overbought';
    } else if (rsi <= this.indicatorConfig.rsi.oversold) {
      return 'oversold';
    }
    return 'neutral';
  }

  /**
   * Get MACD signal (bullish/bearish crossover)
   */
  public getMACDSignal(
    current: any,
    previous: any
  ): 'bullish_crossover' | 'bearish_crossover' | 'neutral' {
    if (!current || !previous) return 'neutral';

    const currentCross = current.macd > current.signal;
    const previousCross = previous.macd > previous.signal;

    if (currentCross && !previousCross) {
      return 'bullish_crossover';
    } else if (!currentCross && previousCross) {
      return 'bearish_crossover';
    }

    return 'neutral';
  }

  /**
   * Get Bollinger Bands signal
   */
  public getBollingerSignal(
    price: number,
    bb: any
  ): 'upper_break' | 'lower_break' | 'squeeze' | 'neutral' {
    if (!bb) return 'neutral';

    const bandWidth = (bb.upper - bb.lower) / bb.middle;

    if (bandWidth < 0.02) {
      // Squeeze threshold
      return 'squeeze';
    } else if (price > bb.upper) {
      return 'upper_break';
    } else if (price < bb.lower) {
      return 'lower_break';
    }

    return 'neutral';
  }

  /**
   * Get trend direction based on EMA and price
   */
  public getTrendDirection(
    price: number,
    ema: number
  ): 'bullish' | 'bearish' | 'sideways' {
    const threshold = 0.001; // 0.1% threshold
    const diff = (price - ema) / ema;

    if (diff > threshold) {
      return 'bullish';
    } else if (diff < -threshold) {
      return 'bearish';
    }

    return 'sideways';
  }

  /**
   * Calculate indicator strength/confidence
   */
  public calculateConfidence(indicators: TechnicalIndicators): number {
    let confidence = 0;
    let factors = 0;

    // RSI factor
    const rsiSignal = this.getRSISignal(indicators.rsi);
    if (rsiSignal !== 'neutral') {
      confidence += rsiSignal === 'oversold' ? 25 : -25; // Oversold is bullish
      factors++;
    }

    // MACD factor
    if (indicators.macd.histogram > 0) {
      confidence += 25;
    } else {
      confidence -= 25;
    }
    factors++;

    // Bollinger Bands factor
    const bbSignal = this.getBollingerSignal(
      indicators.rsi,
      indicators.bollingerBands
    ); // Using RSI as proxy for price
    if (bbSignal === 'lower_break') {
      confidence += 25;
    } else if (bbSignal === 'upper_break') {
      confidence -= 25;
    }
    factors++;

    // Normalize confidence to 0-100 scale
    const normalizedConfidence = Math.abs(confidence / factors);
    return Math.min(100, Math.max(0, normalizedConfidence));
  }

  /**
   * Update indicator configuration
   */
  public updateConfig(newConfig: Partial<IndicatorConfig>): void {
    this.indicatorConfig = { ...this.indicatorConfig, ...newConfig };
    logger.indicators('info', 'Indicator configuration updated', {
      config: this.indicatorConfig
    });
  }

  /**
   * Get current configuration
   */
  public getConfig(): IndicatorConfig {
    return { ...this.indicatorConfig };
  }

  /**
   * Clear data for a symbol
   */
  public clearData(symbol: string): void {
    this.dataBuffers.delete(symbol);
    this.lastCalculation.delete(symbol);
    logger.indicators('info', `Cleared data for ${symbol}`);
  }

  /**
   * Get all tracked symbols
   */
  public getTrackedSymbols(): string[] {
    return Array.from(this.dataBuffers.keys());
  }
}

// Export singleton instance
export const technicalIndicatorsEngine = new TechnicalIndicatorsEngine();
